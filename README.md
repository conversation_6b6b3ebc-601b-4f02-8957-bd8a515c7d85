# <PERSON>'s Stage Analysis with W<PERSON><PERSON>off Method

This Python implementation combines <PERSON>'s Stage Analysis with Wyckoff Method principles to identify accumulation (Stage 1) and advancing (Stage 2) phases in stock price movements.

## Features

- **Stage 1 Detection (Accumulation)**: Identifies stocks in consolidation phase around 200-day MA
- **Stage 2 Detection (Advancing)**: Identifies stocks breaking out with volume confirmation
- **Stage Transition Detection**: Finds recent Stage 1 → Stage 2 transitions
- **Portfolio Analysis**: Analyzes multiple stocks simultaneously
- **Detailed Stock Analysis**: In-depth analysis for individual stocks

## Requirements

```bash
pip install pandas numpy mysql-connector-python
```

Or install all requirements:
```bash
pip install -r requirements.txt
```

## Data Format

Your DataFrame should contain these columns:
- `PREV_CLOSE`: Previous day's closing price
- `OPEN_PRICE`: Opening price
- `CLOSE_PRICE`: Closing price
- `HIGH_PRICE`: High price
- `LOW_PRICE`: Low price
- `DELIV_PER`: Delivery percentage
- `DELIV_QTY`: Delivery quantity
- `TTL_TRD_QNTY`: Total traded quantity (volume)
- `NO_OF_TRADES`: Number of trades
- `DATE1`: Date column (should be set as index)

## Usage

### MySQL Integration (Recommended)

```python
from mysql_stage_analysis import analyze_stocks_from_mysql

# MySQL configuration
mysql_config = {
    'user': 'root',
    'password': 'rootroot',
    'host': 'localhost',
    'database': 'delivery',
    'raise_on_warnings': True
}

# Analyze stocks directly from MySQL
symbols = ['RELIANCE', 'TCS', 'INFY', 'HDFC']
recent_stage2, current_stage1, stock_data = analyze_stocks_from_mysql(
    symbols=symbols,
    mysql_config=mysql_config,
    lookback_days=5,
    limit=252  # ~1 year of data
)
```

### Basic Usage (with DataFrame)

```python
from stage_analysis import StageAnalyzer

# Initialize analyzer
analyzer = StageAnalyzer(ma_period=200, volatility_window=20, volume_window=20)

# Analyze your stock data dictionary
recent_stage2, current_stage1 = analyzer.analyze_stocks(your_data_dict, lookback_days=5)

# Print results
print(f"Recent Stage 2 entries: {len(recent_stage2)}")
print(f"Current Stage 1 stocks: {len(current_stage1)}")
```

### Running the Examples

1. **Basic Analysis**:
```bash
python3 stage_analysis.py
```

2. **Detailed Portfolio Analysis**:
```bash
python3 example_usage.py
```

## Stage Analysis Criteria

### Stage 1 (Accumulation)
- Price within ±5% of 200-day MA
- Low volatility (below average)
- MA200 relatively flat
- No strong breakouts (price change < 10% in 5 days)
- Volume not significantly elevated (< 1.5x average)

### Stage 2 (Advancing)
- Price above 200-day MA by at least 2%
- Positive MA200 slope (upward trend)
- Volume above average (≥ 1.2x average)
- Positive 5-day momentum

### Stage 1 → Stage 2 Transition
- Previous Stage 1 condition followed by Stage 2 condition
- OR simultaneous Stage 1 and Stage 2 conditions (transition period)

## Key Technical Indicators

1. **200-day Moving Average**: Primary trend indicator
2. **MA200 Slope**: Trend direction (5-day slope)
3. **Price vs MA**: Percentage distance from MA200
4. **Average True Range (ATR)**: Volatility measure
5. **Volume Ratio**: Current volume vs 20-day average
6. **Price Momentum**: 5-day and 10-day price changes

## Output Format

### Recent Stage 2 Entries
```python
{
    'symbol': 'STOCK_NAME',
    'entry_date': datetime,
    'entry_price': float,
    'current_price': float,
    'ma200': float,
    'volume_ratio': float
}
```

### Current Stage 1 Stocks
```python
{
    'symbol': 'STOCK_NAME',
    'current_price': float,
    'ma200': float,
    'price_vs_ma': float,
    'volatility': float,
    'volume_ratio': float
}
```

## Customization

You can adjust the analysis parameters:

```python
analyzer = StageAnalyzer(
    ma_period=200,        # Moving average period
    volatility_window=20, # ATR calculation window
    volume_window=20      # Volume average window
)
```

## Integration with Your Data

Replace the dummy data generation in `example_usage.py` with your actual data loading:

```python
def load_your_data():
    stock_data = {}
    for symbol in your_stock_list:
        df = pd.read_csv(f'data/{symbol}.csv')
        df['DATE1'] = pd.to_datetime(df['DATE1'])
        df.set_index('DATE1', inplace=True)
        stock_data[symbol] = df
    return stock_data
```

## Interpretation

- **Stage 1 Stocks**: Potential accumulation candidates, watch for breakout
- **Recent Stage 2 Entries**: Stocks that recently broke out, potential momentum plays
- **Volume Confirmation**: Higher volume ratios indicate stronger conviction
- **Price vs MA**: Shows strength relative to long-term trend

## Risk Considerations

- This is a technical analysis tool, not investment advice
- Always combine with fundamental analysis
- Consider market conditions and sector trends
- Use proper risk management and position sizing
- Backtest strategies before live implementation

## Files

- `stage_analysis.py`: Main analysis class and dummy data generator
- `mysql_data_loader.py`: MySQL database integration for loading stock data
- `mysql_stage_analysis.py`: Complete pipeline combining MySQL data with stage analysis
- `test_mysql_connection.py`: Test MySQL connection and data loading
- `example_usage.py`: Detailed usage examples and portfolio analysis
- `requirements.txt`: Python dependencies
- `README.md`: This documentation

## License

This implementation is for educational and research purposes.
