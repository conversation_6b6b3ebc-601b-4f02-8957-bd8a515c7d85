"""
Usage Examples for Complete Stage Analysis System
This file shows different ways to use the complete_stage_analysis.py script
"""

from complete_stage_analysis import (
    analyze_stocks_from_mysql, 
    analyze_single_stock,
    get_high_delivery_stocks,
    display_analysis_results,
    StageAnalyzer,
    generate_dummy_data
)

def example_1_basic_mysql_analysis():
    """Example 1: Basic MySQL analysis with specific symbols"""
    
    print("📊 EXAMPLE 1: Basic MySQL Analysis")
    print("=" * 50)
    
    # Your MySQL configuration
    mysql_config = {
        'user': 'root',
        'password': 'rootroot',
        'host': 'localhost',
        'database': 'delivery',
        'raise_on_warnings': True
    }
    
    # Symbols to analyze
    symbols = ['RELIANCE', 'TCS', 'INFY', 'HDFC', 'ICICI']
    
    try:
        # Perform analysis
        recent_stage2, current_stage1, stock_data = analyze_stocks_from_mysql(
            symbols=symbols,
            mysql_config=mysql_config,
            lookback_days=5,
            limit=252
        )
        
        # Display results
        display_analysis_results(recent_stage2, current_stage1, stock_data)
        
        return recent_stage2, current_stage1
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return [], []

def example_2_high_delivery_analysis():
    """Example 2: Analyze stocks with high delivery percentage"""
    
    print("\n📦 EXAMPLE 2: High Delivery Percentage Analysis")
    print("=" * 50)
    
    mysql_config = {
        'user': 'root',
        'password': 'rootroot',
        'host': 'localhost',
        'database': 'delivery',
        'raise_on_warnings': True
    }
    
    try:
        # Get stocks with high delivery percentage
        high_delivery_symbols = get_high_delivery_stocks(
            mysql_config, 
            min_delivery_pct=60,  # At least 60% delivery
            limit=10
        )
        
        if high_delivery_symbols:
            print(f"Found high delivery stocks: {high_delivery_symbols}")
            
            # Analyze these stocks
            recent_stage2, current_stage1, stock_data = analyze_stocks_from_mysql(
                symbols=high_delivery_symbols,
                mysql_config=mysql_config,
                lookback_days=7,  # Look back 7 days
                limit=300  # More data for better analysis
            )
            
            display_analysis_results(recent_stage2, current_stage1, stock_data)
            
            return recent_stage2, current_stage1
        else:
            print("No high delivery stocks found")
            return [], []
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return [], []

def example_3_single_stock_detailed():
    """Example 3: Detailed analysis of a single stock"""
    
    print("\n🔍 EXAMPLE 3: Single Stock Detailed Analysis")
    print("=" * 50)
    
    mysql_config = {
        'user': 'root',
        'password': 'rootroot',
        'host': 'localhost',
        'database': 'delivery',
        'raise_on_warnings': True
    }
    
    # Analyze a specific stock in detail
    symbol = 'RELIANCE'
    
    try:
        result = analyze_single_stock(
            symbol=symbol,
            mysql_config=mysql_config,
            limit=365  # 1+ year of data
        )
        
        if result:
            print(f"\n📈 Summary for {symbol}:")
            print(f"Current Stage: {'Stage 1' if result['stage1'] else 'Stage 2' if result['stage2'] else 'Other'}")
            print(f"Quality Score: {result['price_vs_ma']:.2f}% from MA200")
            print(f"Delivery %: {result['delivery_pct']:.1f}%")
        
        return result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def example_4_custom_parameters():
    """Example 4: Using custom analysis parameters"""
    
    print("\n⚙️  EXAMPLE 4: Custom Parameters Analysis")
    print("=" * 50)
    
    mysql_config = {
        'user': 'root',
        'password': 'rootroot',
        'host': 'localhost',
        'database': 'delivery',
        'raise_on_warnings': True
    }
    
    # Custom analyzer with different parameters
    custom_analyzer = StageAnalyzer(
        ma_period=150,        # 150-day MA instead of 200
        volatility_window=15, # 15-day volatility window
        volume_window=15      # 15-day volume window
    )
    
    symbols = ['TCS', 'INFY', 'WIPRO']
    
    try:
        from complete_stage_analysis import MySQLDataLoader
        
        # Load data manually
        loader = MySQLDataLoader(mysql_config)
        stock_data = loader.load_multiple_stocks(symbols, limit=200)
        loader.disconnect()
        
        if stock_data:
            # Analyze with custom parameters
            recent_stage2, current_stage1 = custom_analyzer.analyze_stocks(
                stock_data, 
                lookback_days=10  # Look back 10 days
            )
            
            print(f"Custom Analysis Results:")
            print(f"Recent Stage 2 entries: {len(recent_stage2)}")
            print(f"Current Stage 1 stocks: {len(current_stage1)}")
            
            # Show brief results
            for entry in recent_stage2:
                print(f"  📈 {entry['symbol']}: Entry on {entry['entry_date'].strftime('%Y-%m-%d')}")
            
            for stock in current_stage1:
                print(f"  🔄 {stock['symbol']}: {stock['price_vs_ma']:+.2f}% from MA150")
            
            return recent_stage2, current_stage1
        else:
            print("No data loaded")
            return [], []
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return [], []

def example_5_dummy_data_testing():
    """Example 5: Testing with dummy data (no MySQL required)"""
    
    print("\n🧪 EXAMPLE 5: Dummy Data Testing")
    print("=" * 50)
    
    # Generate dummy data for testing
    test_symbols = ['TEST_A', 'TEST_B', 'TEST_C', 'TEST_D']
    dummy_data = generate_dummy_data(test_symbols, start_date='2023-01-01', end_date='2024-12-31')
    
    # Analyze with dummy data
    analyzer = StageAnalyzer()
    recent_stage2, current_stage1 = analyzer.analyze_stocks(dummy_data, lookback_days=5)
    
    print(f"Dummy Data Analysis Results:")
    print(f"Stocks analyzed: {len(dummy_data)}")
    print(f"Recent Stage 2 entries: {len(recent_stage2)}")
    print(f"Current Stage 1 stocks: {len(current_stage1)}")
    
    # Display brief results
    display_analysis_results(recent_stage2, current_stage1, dummy_data)
    
    return recent_stage2, current_stage1

def example_6_portfolio_screening():
    """Example 6: Portfolio screening workflow"""
    
    print("\n🎯 EXAMPLE 6: Portfolio Screening Workflow")
    print("=" * 50)
    
    mysql_config = {
        'user': 'root',
        'password': 'rootroot',
        'host': 'localhost',
        'database': 'delivery',
        'raise_on_warnings': True
    }
    
    # Step 1: Get all available symbols (or use a predefined list)
    try:
        from complete_stage_analysis import MySQLDataLoader
        
        loader = MySQLDataLoader(mysql_config)
        if loader.connect():
            all_symbols = loader.get_available_symbols(limit=50)  # Get first 50 symbols
            loader.disconnect()
        else:
            # Fallback to common symbols
            all_symbols = [
                'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'HINDUNILVR',
                'ICICIBANK', 'KOTAKBANK', 'BHARTIARTL', 'ITC', 'SBIN'
            ]
        
        print(f"Screening {len(all_symbols)} stocks...")
        
        # Step 2: Analyze all stocks
        recent_stage2, current_stage1, stock_data = analyze_stocks_from_mysql(
            symbols=all_symbols,
            mysql_config=mysql_config,
            lookback_days=5,
            limit=252
        )
        
        # Step 3: Filter and rank results
        print(f"\n🏆 TOP OPPORTUNITIES:")
        print("-" * 40)
        
        # Rank Stage 2 entries by gain/loss
        if recent_stage2:
            stage2_ranked = sorted(recent_stage2, 
                                 key=lambda x: ((x['current_price'] - x['entry_price']) / x['entry_price']), 
                                 reverse=True)
            
            print("📈 Best Stage 2 Performers:")
            for i, entry in enumerate(stage2_ranked[:5], 1):
                gain = ((entry['current_price'] - entry['entry_price']) / entry['entry_price']) * 100
                print(f"  {i}. {entry['symbol']}: {gain:+.2f}%")
        
        # Rank Stage 1 stocks by accumulation quality
        if current_stage1:
            stage1_ranked = sorted(current_stage1, key=lambda x: abs(x['price_vs_ma']))
            
            print("\n🔄 Best Stage 1 Candidates:")
            for i, stock in enumerate(stage1_ranked[:5], 1):
                print(f"  {i}. {stock['symbol']}: {stock['price_vs_ma']:+.2f}% from MA200")
        
        return recent_stage2, current_stage1
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return [], []

def main():
    """Run all examples"""
    
    print("🚀 COMPLETE STAGE ANALYSIS - USAGE EXAMPLES")
    print("=" * 60)
    
    # Run examples (comment out MySQL examples if database not available)
    
    # Example 1: Basic MySQL analysis
    try:
        example_1_basic_mysql_analysis()
    except:
        print("❌ Example 1 failed - MySQL not available")
    
    # Example 2: High delivery analysis
    try:
        example_2_high_delivery_analysis()
    except:
        print("❌ Example 2 failed - MySQL not available")
    
    # Example 3: Single stock detailed
    try:
        example_3_single_stock_detailed()
    except:
        print("❌ Example 3 failed - MySQL not available")
    
    # Example 4: Custom parameters
    try:
        example_4_custom_parameters()
    except:
        print("❌ Example 4 failed - MySQL not available")
    
    # Example 5: Dummy data (always works)
    example_5_dummy_data_testing()
    
    # Example 6: Portfolio screening
    try:
        example_6_portfolio_screening()
    except:
        print("❌ Example 6 failed - MySQL not available")
    
    print("\n✅ All examples completed!")
    print("\n💡 To use with your MySQL database:")
    print("   1. Update mysql_config with your credentials")
    print("   2. Install: pip install mysql-connector-python")
    print("   3. Run individual examples or modify for your needs")

if __name__ == "__main__":
    main()
