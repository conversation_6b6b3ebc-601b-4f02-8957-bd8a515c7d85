"""
Example using your exact MySQL setup for Stage Analysis
This script demonstrates how to use your existing MySQL database structure
"""

import pandas as pd
import mysql.connector
from datetime import datetime, timedelta
from stage_analysis import <PERSON><PERSON><PERSON><PERSON><PERSON>

def load_stock_data_your_way(symbol, recent_date=None, limit=252):
    """
    Load stock data using your exact MySQL setup
    
    Args:
        symbol (str): Stock symbol
        recent_date (str): Recent date in 'YYYY-MM-DD' format
        limit (int): Number of records to fetch
    
    Returns:
        pandas.DataFrame: Stock data ready for stage analysis
    """
    
    # Your MySQL configuration
    config = {
        'user': 'root',
        'password': 'rootroot',
        'host': 'localhost',
        'database': 'delivery',
        'raise_on_warnings': True
    }
    
    # Set default recent_date if not provided
    if recent_date is None:
        recent_date = datetime.now().strftime('%Y-%m-%d')
    
    # Column names for DataFrame
    column_names = [
        'PREV_CLOSE', 'OPEN_PRICE', 'CLOSE_PRICE', 'HIGH_PRICE', 'LOW_PRICE',
        'DELIV_PER', 'DELIV_QTY', 'TTL_TRD_QNTY', 'NO_OF_TRADES', 'DATE1'
    ]
    
    try:
        # Establish the database connection (your exact code)
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        # Your exact query
        query1 = """SELECT PREV_CLOSE,OPEN_PRICE,CLOSE_PRICE,HIGH_PRICE,LOW_PRICE,
                           DELIV_PER,DELIV_QTY,TTL_TRD_QNTY,NO_OF_TRADES, DATE1 
                    FROM `analysis` 
                    WHERE SYMBOL = %s AND DATE1 <= %s 
                    ORDER BY DATE1 DESC 
                    LIMIT %s"""
        
        values_252 = (symbol, recent_date, limit)
        
        # Execute query (your exact code)
        cursor.execute(query1, values_252)
        rows = cursor.fetchall()
        
        # Create DataFrame (your exact code)
        df = pd.DataFrame(rows, columns=column_names)
        
        if df.empty:
            print(f"⚠️  No data found for {symbol}")
            return None
        
        # Prepare data for stage analysis
        df['DATE1'] = pd.to_datetime(df['DATE1'])
        df.set_index('DATE1', inplace=True)
        df.sort_index(inplace=True)  # Sort chronologically for analysis
        
        # Convert to numeric types
        numeric_columns = [
            'PREV_CLOSE', 'OPEN_PRICE', 'CLOSE_PRICE', 'HIGH_PRICE', 'LOW_PRICE',
            'DELIV_PER', 'DELIV_QTY', 'TTL_TRD_QNTY', 'NO_OF_TRADES'
        ]
        
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        print(f"✅ Loaded {len(df)} records for {symbol}")
        return df
        
    except mysql.connector.Error as e:
        print(f"❌ MySQL Error for {symbol}: {e}")
        return None
    except Exception as e:
        print(f"❌ Error loading {symbol}: {e}")
        return None
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def analyze_your_stocks(symbols, recent_date=None, lookback_days=5):
    """
    Analyze multiple stocks using your MySQL setup
    
    Args:
        symbols (list): List of stock symbols
        recent_date (str): Recent date in 'YYYY-MM-DD' format
        lookback_days (int): Days to look back for Stage 2 entries
    
    Returns:
        tuple: (recent_stage2_entries, current_stage1_stocks)
    """
    
    print(f"🔍 ANALYZING {len(symbols)} STOCKS FROM YOUR MYSQL DATABASE")
    print("=" * 60)
    
    # Load data for all symbols
    stock_data = {}
    for symbol in symbols:
        print(f"Loading {symbol}...", end=" ")
        df = load_stock_data_your_way(symbol, recent_date, limit=252)
        
        if df is not None and len(df) >= 200:  # Need sufficient data
            stock_data[symbol] = df
        else:
            print("❌ (insufficient data)")
    
    if not stock_data:
        print("❌ No valid data loaded")
        return [], []
    
    print(f"\n📊 Successfully loaded {len(stock_data)} stocks")
    
    # Perform stage analysis
    analyzer = StageAnalyzer()
    recent_stage2, current_stage1 = analyzer.analyze_stocks(stock_data, lookback_days)
    
    return recent_stage2, current_stage1

def display_results_with_delivery_data(recent_stage2, current_stage1, symbols):
    """Display results with additional delivery data insights"""
    
    print(f"\n📈 RECENT STAGE 2 ENTRIES: {len(recent_stage2)} stocks")
    print("-" * 60)
    
    if recent_stage2:
        for i, entry in enumerate(recent_stage2, 1):
            gain_loss = ((entry['current_price'] - entry['entry_price']) / entry['entry_price']) * 100
            
            # Load latest data to get delivery info
            latest_df = load_stock_data_your_way(entry['symbol'], limit=1)
            if latest_df is not None and not latest_df.empty:
                latest_data = latest_df.iloc[-1]
                delivery_pct = latest_data['DELIV_PER']
                num_trades = latest_data['NO_OF_TRADES']
            else:
                delivery_pct = 0
                num_trades = 0
            
            print(f"{i}. {entry['symbol']}")
            print(f"   📅 Entry Date: {entry['entry_date'].strftime('%Y-%m-%d')}")
            print(f"   💰 Entry Price: ₹{entry['entry_price']:.2f}")
            print(f"   📊 Current Price: ₹{entry['current_price']:.2f}")
            print(f"   📈 Gain/Loss: {gain_loss:+.2f}%")
            print(f"   📉 MA200: ₹{entry['ma200']:.2f}")
            print(f"   📊 Volume Ratio: {entry['volume_ratio']:.2f}x")
            print(f"   📦 Delivery %: {delivery_pct:.1f}%")
            print(f"   🔢 No. of Trades: {num_trades:,}")
            
            # Quality assessment
            quality_indicators = []
            if gain_loss > 0:
                quality_indicators.append("✅ Positive momentum")
            if entry['volume_ratio'] > 1.5:
                quality_indicators.append("✅ Strong volume")
            if delivery_pct > 50:
                quality_indicators.append("✅ High delivery %")
            
            if quality_indicators:
                print(f"   🎯 Quality: {', '.join(quality_indicators)}")
            print()
    else:
        print("   No recent Stage 2 entries found.")
    
    print(f"\n🔄 CURRENT STAGE 1 STOCKS: {len(current_stage1)} stocks")
    print("-" * 60)
    
    if current_stage1:
        for i, stock in enumerate(current_stage1, 1):
            # Load latest data for delivery info
            latest_df = load_stock_data_your_way(stock['symbol'], limit=1)
            if latest_df is not None and not latest_df.empty:
                latest_data = latest_df.iloc[-1]
                delivery_pct = latest_data['DELIV_PER']
                num_trades = latest_data['NO_OF_TRADES']
            else:
                delivery_pct = 0
                num_trades = 0
            
            print(f"{i}. {stock['symbol']}")
            print(f"   💰 Current Price: ₹{stock['current_price']:.2f}")
            print(f"   📉 MA200: ₹{stock['ma200']:.2f}")
            print(f"   📊 Price vs MA: {stock['price_vs_ma']:+.2f}%")
            print(f"   📈 Volatility: {stock['volatility']:.2f}%")
            print(f"   📊 Volume Ratio: {stock['volume_ratio']:.2f}x")
            print(f"   📦 Delivery %: {delivery_pct:.1f}%")
            print(f"   🔢 No. of Trades: {num_trades:,}")
            
            # Accumulation quality score
            quality_score = (
                (5 - abs(stock['price_vs_ma'])) * 0.3 +
                (3 - stock['volatility']) * 0.2 +
                (2 - abs(stock['volume_ratio'] - 1)) * 0.2 +
                (delivery_pct / 100) * 0.3  # Higher delivery % is better
            )
            quality_score = max(0, min(10, quality_score))
            
            print(f"   ⭐ Accumulation Quality: {quality_score:.1f}/10")
            print()
    else:
        print("   No stocks currently in Stage 1 accumulation phase.")

def main():
    """Main function using your exact MySQL setup"""
    
    print("🚀 STAGE ANALYSIS WITH YOUR MYSQL DATABASE")
    print("=" * 50)
    
    # Example 1: Analyze specific stocks
    print("\n📋 Analyzing specific stocks...")
    symbols = ['RELIANCE', 'TCS', 'INFY', 'HDFC', 'ICICI']
    
    recent_stage2, current_stage1 = analyze_your_stocks(
        symbols=symbols,
        recent_date=None,  # Use current date
        lookback_days=5
    )
    
    display_results_with_delivery_data(recent_stage2, current_stage1, symbols)
    
    # Example 2: Quick single stock analysis
    print("\n" + "="*70)
    print("📊 QUICK SINGLE STOCK ANALYSIS")
    print("-" * 40)
    
    single_symbol = 'RELIANCE'
    df = load_stock_data_your_way(single_symbol, limit=252)
    
    if df is not None:
        analyzer = StageAnalyzer()
        df_analyzed = analyzer.calculate_indicators(df)
        df_analyzed = analyzer.detect_stage_transitions(df_analyzed)
        
        latest = df_analyzed.iloc[-1]
        
        print(f"Stock: {single_symbol}")
        print(f"Current Price: ₹{latest['CLOSE_PRICE']:.2f}")
        print(f"MA200: ₹{latest['MA200']:.2f}")
        print(f"Price vs MA: {latest['price_vs_ma']:+.2f}%")
        print(f"Volume Ratio: {latest['volume_ratio']:.2f}x")
        print(f"Delivery %: {latest['DELIV_PER']:.1f}%")
        
        if latest['stage1']:
            print("🔄 Current Stage: STAGE 1 (Accumulation)")
        elif latest['stage2']:
            print("📈 Current Stage: STAGE 2 (Advancing)")
        else:
            print("⚠️  Current Stage: Other")
    
    print("\n✅ Analysis complete using your MySQL database!")
    print("\n💡 To customize:")
    print("   - Modify the symbols list")
    print("   - Adjust the recent_date parameter")
    print("   - Change lookback_days for Stage 2 detection")
    print("   - Update MySQL config if needed")

if __name__ == "__main__":
    main()
