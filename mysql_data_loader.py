"""
MySQL Data Loader for Stage Analysis
Loads stock data from MySQL database and prepares it for stage analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

try:
    import mysql.connector
    from mysql.connector import Error
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False
    print("MySQL connector not available. Install with: pip install mysql-connector-python")

class MySQLDataLoader:
    """
    MySQL data loader for stock market data
    """
    
    def __init__(self, config):
        """
        Initialize MySQL connection
        
        Args:
            config (dict): MySQL connection configuration
                {
                    'user': 'root',
                    'password': 'rootroot',
                    'host': 'localhost',
                    'database': 'delivery',
                    'raise_on_warnings': True
                }
        """
        if not MYSQL_AVAILABLE:
            raise ImportError("mysql-connector-python is required. Install with: pip install mysql-connector-python")
        
        self.config = config
        self.connection = None
        self.cursor = None
        
        # Column names for the DataFrame
        self.column_names = [
            'PREV_CLOSE', 'OPEN_PRICE', 'CLOSE_PRICE', 'HIGH_PRICE', 'LOW_PRICE',
            'DELIV_PER', 'DELIV_QTY', 'TTL_TRD_QNTY', 'NO_OF_TRADES', 'DATE1'
        ]
    
    def connect(self):
        """Establish database connection"""
        try:
            self.connection = mysql.connector.connect(**self.config)
            self.cursor = self.connection.cursor()
            print("✅ MySQL connection established successfully")
            return True
        except Error as e:
            print(f"❌ Error connecting to MySQL: {e}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔌 MySQL connection closed")
    
    def load_stock_data(self, symbol, recent_date=None, limit=252):
        """
        Load stock data for a single symbol
        
        Args:
            symbol (str): Stock symbol
            recent_date (str or datetime): Recent date to load data up to
            limit (int): Number of records to fetch (default: 252 for ~1 year)
        
        Returns:
            pandas.DataFrame: Stock data with DATE1 as index
        """
        if not self.connection:
            if not self.connect():
                return None
        
        # Set default recent_date to today if not provided
        if recent_date is None:
            recent_date = datetime.now().strftime('%Y-%m-%d')
        elif isinstance(recent_date, datetime):
            recent_date = recent_date.strftime('%Y-%m-%d')
        
        # SQL query to fetch stock data
        query = """
        SELECT PREV_CLOSE, OPEN_PRICE, CLOSE_PRICE, HIGH_PRICE, LOW_PRICE,
               DELIV_PER, DELIV_QTY, TTL_TRD_QNTY, NO_OF_TRADES, DATE1 
        FROM `analysis` 
        WHERE SYMBOL = %s AND DATE1 <= %s 
        ORDER BY DATE1 DESC 
        LIMIT %s
        """
        
        try:
            values = (symbol, recent_date, limit)
            self.cursor.execute(query, values)
            rows = self.cursor.fetchall()
            
            if not rows:
                print(f"⚠️  No data found for symbol: {symbol}")
                return None
            
            # Create DataFrame
            df = pd.DataFrame(rows, columns=self.column_names)
            
            # Convert DATE1 to datetime and set as index
            df['DATE1'] = pd.to_datetime(df['DATE1'])
            df.set_index('DATE1', inplace=True)
            
            # Sort by date (ascending order for analysis)
            df.sort_index(inplace=True)
            
            # Convert numeric columns to appropriate types
            numeric_columns = [
                'PREV_CLOSE', 'OPEN_PRICE', 'CLOSE_PRICE', 'HIGH_PRICE', 'LOW_PRICE',
                'DELIV_PER', 'DELIV_QTY', 'TTL_TRD_QNTY', 'NO_OF_TRADES'
            ]
            
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            print(f"✅ Loaded {len(df)} records for {symbol} (from {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')})")
            
            return df
            
        except Error as e:
            print(f"❌ Error loading data for {symbol}: {e}")
            return None
    
    def load_multiple_stocks(self, symbols, recent_date=None, limit=252):
        """
        Load data for multiple stock symbols
        
        Args:
            symbols (list): List of stock symbols
            recent_date (str or datetime): Recent date to load data up to
            limit (int): Number of records to fetch per symbol
        
        Returns:
            dict: Dictionary with symbol as key and DataFrame as value
        """
        if not self.connection:
            if not self.connect():
                return {}
        
        stock_data = {}
        successful_loads = 0
        
        print(f"📊 Loading data for {len(symbols)} stocks...")
        
        for i, symbol in enumerate(symbols, 1):
            print(f"Loading {i}/{len(symbols)}: {symbol}...", end=" ")
            
            df = self.load_stock_data(symbol, recent_date, limit)
            
            if df is not None and len(df) >= 200:  # Ensure sufficient data for analysis
                stock_data[symbol] = df
                successful_loads += 1
                print("✅")
            else:
                print("❌ (insufficient data)")
        
        print(f"\n📈 Successfully loaded {successful_loads}/{len(symbols)} stocks")
        return stock_data
    
    def get_available_symbols(self, limit=None):
        """
        Get list of available stock symbols in the database
        
        Args:
            limit (int): Limit number of symbols returned
        
        Returns:
            list: List of available stock symbols
        """
        if not self.connection:
            if not self.connect():
                return []
        
        query = "SELECT DISTINCT SYMBOL FROM `analysis` ORDER BY SYMBOL"
        if limit:
            query += f" LIMIT {limit}"
        
        try:
            self.cursor.execute(query)
            symbols = [row[0] for row in self.cursor.fetchall()]
            print(f"📋 Found {len(symbols)} unique symbols in database")
            return symbols
        except Error as e:
            print(f"❌ Error fetching symbols: {e}")
            return []
    
    def get_date_range(self, symbol=None):
        """
        Get the date range of available data
        
        Args:
            symbol (str): Specific symbol to check (optional)
        
        Returns:
            tuple: (min_date, max_date)
        """
        if not self.connection:
            if not self.connect():
                return None, None
        
        if symbol:
            query = "SELECT MIN(DATE1), MAX(DATE1) FROM `analysis` WHERE SYMBOL = %s"
            values = (symbol,)
        else:
            query = "SELECT MIN(DATE1), MAX(DATE1) FROM `analysis`"
            values = ()
        
        try:
            self.cursor.execute(query, values)
            result = self.cursor.fetchone()
            min_date, max_date = result if result else (None, None)
            
            if min_date and max_date:
                print(f"📅 Data range: {min_date} to {max_date}")
            
            return min_date, max_date
        except Error as e:
            print(f"❌ Error fetching date range: {e}")
            return None, None

def load_data_for_stage_analysis(symbols, mysql_config, recent_date=None, limit=252):
    """
    Convenience function to load data for stage analysis
    
    Args:
        symbols (list): List of stock symbols
        mysql_config (dict): MySQL connection configuration
        recent_date (str or datetime): Recent date to load data up to
        limit (int): Number of records to fetch per symbol
    
    Returns:
        dict: Dictionary with symbol as key and DataFrame as value
    """
    loader = MySQLDataLoader(mysql_config)
    
    try:
        stock_data = loader.load_multiple_stocks(symbols, recent_date, limit)
        return stock_data
    finally:
        loader.disconnect()

def main():
    """Example usage of MySQL data loader"""
    
    # MySQL configuration
    config = {
        'user': 'root',
        'password': 'rootroot',
        'host': 'localhost',
        'database': 'delivery',
        'raise_on_warnings': True
    }
    
    print("🔍 MYSQL DATA LOADER EXAMPLE")
    print("=" * 50)
    
    # Initialize loader
    loader = MySQLDataLoader(config)
    
    try:
        # Connect to database
        if not loader.connect():
            return
        
        # Get available symbols (first 10)
        symbols = loader.get_available_symbols(limit=10)
        if not symbols:
            print("No symbols found in database")
            return
        
        print(f"Available symbols: {symbols[:5]}...")  # Show first 5
        
        # Get date range
        loader.get_date_range()
        
        # Load data for a few symbols
        test_symbols = symbols[:3]  # Test with first 3 symbols
        stock_data = loader.load_multiple_stocks(test_symbols, limit=252)
        
        # Display summary
        print(f"\n📊 DATA SUMMARY")
        print("-" * 30)
        for symbol, df in stock_data.items():
            print(f"{symbol}:")
            print(f"  Records: {len(df)}")
            print(f"  Date range: {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}")
            print(f"  Price range: ₹{df['CLOSE_PRICE'].min():.2f} - ₹{df['CLOSE_PRICE'].max():.2f}")
            print(f"  Avg volume: {df['TTL_TRD_QNTY'].mean():,.0f}")
            print()
        
    finally:
        loader.disconnect()

if __name__ == "__main__":
    if not MYSQL_AVAILABLE:
        print("Please install mysql-connector-python:")
        print("pip install mysql-connector-python")
    else:
        main()
