"""
Test MySQL connection and data loading
"""

from mysql_data_loader import MySQLDataLoader
import pandas as pd

def test_mysql_connection():
    """Test MySQL connection and basic data loading"""
    
    # MySQL configuration
    config = {
        'user': 'root',
        'password': 'rootroot',
        'host': 'localhost',
        'database': 'delivery',
        'raise_on_warnings': True
    }
    
    print("🧪 TESTING MYSQL CONNECTION")
    print("=" * 50)
    
    # Initialize loader
    loader = MySQLDataLoader(config)
    
    try:
        # Test 1: Connection
        print("Test 1: Database Connection")
        print("-" * 30)
        if loader.connect():
            print("✅ Connection successful")
        else:
            print("❌ Connection failed")
            return False
        
        # Test 2: Get available symbols
        print("\nTest 2: Available Symbols")
        print("-" * 30)
        symbols = loader.get_available_symbols(limit=5)
        if symbols:
            print(f"✅ Found symbols: {symbols}")
        else:
            print("❌ No symbols found")
            return False
        
        # Test 3: Get date range
        print("\nTest 3: Date Range")
        print("-" * 30)
        min_date, max_date = loader.get_date_range()
        if min_date and max_date:
            print(f"✅ Date range: {min_date} to {max_date}")
        else:
            print("❌ Could not get date range")
        
        # Test 4: Load single stock data
        print("\nTest 4: Single Stock Data Loading")
        print("-" * 30)
        test_symbol = symbols[0] if symbols else 'RELIANCE'
        df = loader.load_stock_data(test_symbol, limit=10)
        
        if df is not None and len(df) > 0:
            print(f"✅ Loaded {len(df)} records for {test_symbol}")
            print("Sample data:")
            print(df.head(3))
            
            # Validate data structure
            expected_columns = [
                'PREV_CLOSE', 'OPEN_PRICE', 'CLOSE_PRICE', 'HIGH_PRICE', 'LOW_PRICE',
                'DELIV_PER', 'DELIV_QTY', 'TTL_TRD_QNTY', 'NO_OF_TRADES'
            ]
            
            missing_columns = [col for col in expected_columns if col not in df.columns]
            if missing_columns:
                print(f"⚠️  Missing columns: {missing_columns}")
            else:
                print("✅ All expected columns present")
            
            # Check data types
            numeric_columns = [
                'PREV_CLOSE', 'OPEN_PRICE', 'CLOSE_PRICE', 'HIGH_PRICE', 'LOW_PRICE',
                'TTL_TRD_QNTY', 'NO_OF_TRADES'
            ]
            
            for col in numeric_columns:
                if col in df.columns:
                    if pd.api.types.is_numeric_dtype(df[col]):
                        print(f"✅ {col}: numeric type")
                    else:
                        print(f"⚠️  {col}: non-numeric type ({df[col].dtype})")
            
            # Check for missing values
            missing_values = df.isnull().sum()
            if missing_values.sum() > 0:
                print(f"⚠️  Missing values found:")
                for col, count in missing_values[missing_values > 0].items():
                    print(f"   {col}: {count} missing")
            else:
                print("✅ No missing values")
                
        else:
            print(f"❌ Failed to load data for {test_symbol}")
        
        # Test 5: Load multiple stocks
        print("\nTest 5: Multiple Stocks Loading")
        print("-" * 30)
        test_symbols = symbols[:3] if len(symbols) >= 3 else symbols
        stock_data = loader.load_multiple_stocks(test_symbols, limit=50)
        
        if stock_data:
            print(f"✅ Successfully loaded {len(stock_data)} stocks")
            for symbol, df in stock_data.items():
                print(f"   {symbol}: {len(df)} records")
        else:
            print("❌ Failed to load multiple stocks")
        
        print("\n🎯 TEST SUMMARY")
        print("-" * 30)
        print("✅ MySQL connection working")
        print("✅ Data loading functional")
        print("✅ Data structure validated")
        print("\n🚀 Ready for Stage Analysis!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
        
    finally:
        loader.disconnect()

def test_with_stage_analysis():
    """Test MySQL data loading with stage analysis"""
    
    print("\n" + "="*70)
    print("🔬 TESTING MYSQL + STAGE ANALYSIS INTEGRATION")
    print("=" * 70)
    
    try:
        from mysql_stage_analysis import analyze_stocks_from_mysql
        
        # MySQL configuration
        mysql_config = {
            'user': 'root',
            'password': 'rootroot',
            'host': 'localhost',
            'database': 'delivery',
            'raise_on_warnings': True
        }
        
        # Test with a few symbols
        test_symbols = ['RELIANCE', 'TCS']  # Start with common symbols
        
        recent_stage2, current_stage1, stock_data = analyze_stocks_from_mysql(
            symbols=test_symbols,
            mysql_config=mysql_config,
            lookback_days=5,
            limit=252
        )
        
        print(f"\n📊 INTEGRATION TEST RESULTS")
        print("-" * 40)
        print(f"Stocks analyzed: {len(stock_data)}")
        print(f"Recent Stage 2 entries: {len(recent_stage2)}")
        print(f"Current Stage 1 stocks: {len(current_stage1)}")
        
        if stock_data:
            print("\n✅ Integration test successful!")
            print("🎯 MySQL + Stage Analysis pipeline working correctly")
        else:
            print("\n❌ Integration test failed - no data loaded")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all required modules are available")
    except Exception as e:
        print(f"❌ Integration test failed: {e}")

def main():
    """Run all tests"""
    
    # Test basic MySQL functionality
    connection_success = test_mysql_connection()
    
    if connection_success:
        # Test integration with stage analysis
        test_with_stage_analysis()
    else:
        print("\n❌ Basic MySQL tests failed. Please check:")
        print("   1. MySQL server is running")
        print("   2. Database 'delivery' exists")
        print("   3. Table 'analysis' exists with correct structure")
        print("   4. MySQL credentials are correct")
        print("   5. mysql-connector-python is installed")

if __name__ == "__main__":
    main()
