import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class StageAnalyzer:
    """
    <PERSON>'s Stage Analysis with Wyckoff Method principles
    Adapted for daily data with 200-day moving average
    """
    
    def __init__(self, ma_period=200, volatility_window=20, volume_window=20):
        self.ma_period = ma_period
        self.volatility_window = volatility_window
        self.volume_window = volume_window
    
    def calculate_indicators(self, df):
        """Calculate technical indicators needed for stage analysis"""
        df = df.copy()
        
        # 200-day moving average
        df['MA200'] = df['CLOSE_PRICE'].rolling(window=self.ma_period).mean()
        
        # MA slope (trend direction)
        df['MA200_slope'] = df['MA200'].diff(5) / 5  # 5-day slope
        
        # Price relative to MA200
        df['price_vs_ma'] = (df['CLOSE_PRICE'] - df['MA200']) / df['MA200'] * 100
        
        # Volatility (Average True Range percentage)
        df['high_low'] = df['HIGH_PRICE'] - df['LOW_PRICE']
        df['high_close'] = abs(df['HIGH_PRICE'] - df['CLOSE_PRICE'].shift(1))
        df['low_close'] = abs(df['LOW_PRICE'] - df['CLOSE_PRICE'].shift(1))
        df['true_range'] = df[['high_low', 'high_close', 'low_close']].max(axis=1)
        df['atr'] = df['true_range'].rolling(window=14).mean()
        df['volatility_pct'] = (df['atr'] / df['CLOSE_PRICE']) * 100
        
        # Volume indicators
        df['volume_ma'] = df['TTL_TRD_QNTY'].rolling(window=self.volume_window).mean()
        df['volume_ratio'] = df['TTL_TRD_QNTY'] / df['volume_ma']
        
        # Price momentum
        df['price_change_5d'] = df['CLOSE_PRICE'].pct_change(5) * 100
        df['price_change_10d'] = df['CLOSE_PRICE'].pct_change(10) * 100
        
        return df
    
    def detect_stage1_accumulation(self, df):
        """
        Detect Stage 1 (Accumulation) conditions:
        - Price moving sideways around 200MA
        - Low volatility
        - Decreasing or stable volume
        - No strong breakouts
        """
        conditions = (
            # Price near 200MA (within ±5%)
            (abs(df['price_vs_ma']) <= 5) &
            
            # Low volatility (below average)
            (df['volatility_pct'] <= df['volatility_pct'].rolling(50).mean()) &
            
            # MA200 is relatively flat (slope close to zero)
            (abs(df['MA200_slope']) <= df['CLOSE_PRICE'] * 0.001) &
            
            # No strong recent breakouts (price change < 10% in 5 days)
            (abs(df['price_change_5d']) <= 10) &
            
            # Volume not significantly elevated
            (df['volume_ratio'] <= 1.5)
        )
        
        return conditions
    
    def detect_stage2_advancing(self, df):
        """
        Detect Stage 2 (Advancing) conditions:
        - Price breaking above 200MA
        - Rising volume
        - 200MA turning upward
        """
        conditions = (
            # Price above 200MA
            (df['CLOSE_PRICE'] > df['MA200']) &
            
            # Price significantly above MA (at least 2%)
            (df['price_vs_ma'] >= 2) &
            
            # MA200 slope is positive (upward trend)
            (df['MA200_slope'] > 0) &
            
            # Volume above average (confirmation)
            (df['volume_ratio'] >= 1.2) &
            
            # Positive momentum
            (df['price_change_5d'] > 0)
        )
        
        return conditions
    
    def detect_stage_transitions(self, df):
        """Detect transitions from Stage 1 to Stage 2"""
        df = df.copy()

        # Calculate stage conditions
        df['stage1'] = self.detect_stage1_accumulation(df)
        df['stage2'] = self.detect_stage2_advancing(df)

        # Detect Stage 1 to Stage 2 transitions
        df['stage1_prev'] = df['stage1'].shift(1).fillna(False)
        df['stage2_entry'] = ((~df['stage1_prev']) & df['stage2']) | (df['stage1_prev'] & df['stage2'])

        return df
    
    def analyze_stocks(self, data_dict, lookback_days=5):
        """
        Analyze multiple stocks and return Stage 1 and recent Stage 2 entries
        
        Args:
            data_dict: Dictionary with stock symbols as keys and DataFrames as values
            lookback_days: Number of days to look back for recent Stage 2 entries
        
        Returns:
            tuple: (recent_stage2_entries, current_stage1_stocks)
        """
        recent_stage2_entries = []
        current_stage1_stocks = []
        
        for symbol, df in data_dict.items():
            if len(df) < self.ma_period + 50:  # Need enough data
                continue
                
            # Calculate indicators and detect stages
            df_analyzed = self.calculate_indicators(df)
            df_analyzed = self.detect_stage_transitions(df_analyzed)
            
            # Get recent data (last lookback_days)
            recent_data = df_analyzed.tail(lookback_days)
            latest_data = df_analyzed.iloc[-1]
            
            # Check for recent Stage 2 entries
            if recent_data['stage2_entry'].any():
                stage2_entry_date = recent_data[recent_data['stage2_entry']].index[-1]
                recent_stage2_entries.append({
                    'symbol': symbol,
                    'entry_date': stage2_entry_date,
                    'entry_price': df_analyzed.loc[stage2_entry_date, 'CLOSE_PRICE'],
                    'current_price': latest_data['CLOSE_PRICE'],
                    'ma200': latest_data['MA200'],
                    'volume_ratio': latest_data['volume_ratio']
                })
            
            # Check for current Stage 1 stocks
            if latest_data['stage1']:
                current_stage1_stocks.append({
                    'symbol': symbol,
                    'current_price': latest_data['CLOSE_PRICE'],
                    'ma200': latest_data['MA200'],
                    'price_vs_ma': latest_data['price_vs_ma'],
                    'volatility': latest_data['volatility_pct'],
                    'volume_ratio': latest_data['volume_ratio']
                })
        
        return recent_stage2_entries, current_stage1_stocks

def generate_dummy_data(symbols, start_date='2023-01-01', end_date='2024-12-31'):
    """Generate dummy OHLCV data for testing"""
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    # Remove weekends (assuming stock market data)
    date_range = date_range[date_range.weekday < 5]
    
    data_dict = {}
    
    for symbol in symbols:
        np.random.seed(hash(symbol) % 2**32)  # Consistent random data per symbol
        
        n_days = len(date_range)
        
        # Generate base price trend
        trend = np.cumsum(np.random.normal(0.001, 0.02, n_days))
        base_price = 100 * np.exp(trend)
        
        # Add some cyclical patterns for stage analysis
        cycle = 10 * np.sin(np.arange(n_days) * 2 * np.pi / 200)
        base_price += cycle
        
        # Generate OHLCV data
        volatility = np.random.uniform(0.01, 0.03, n_days)
        
        close_prices = base_price
        open_prices = close_prices * (1 + np.random.normal(0, volatility/2))
        high_prices = np.maximum(open_prices, close_prices) * (1 + np.random.uniform(0, volatility))
        low_prices = np.minimum(open_prices, close_prices) * (1 - np.random.uniform(0, volatility))
        
        # Volume (correlated with price movements)
        price_changes = np.abs(np.diff(close_prices, prepend=close_prices[0]))
        base_volume = 1000000
        volumes = base_volume * (1 + price_changes / np.mean(price_changes)) * np.random.uniform(0.5, 2.0, n_days)
        
        # Delivery data
        deliv_per = np.random.uniform(20, 80, n_days)
        deliv_qty = volumes * deliv_per / 100
        
        # Number of trades
        no_of_trades = (volumes / 1000 * np.random.uniform(0.8, 1.2, n_days)).astype(int)
        
        df = pd.DataFrame({
            'DATE1': date_range,
            'PREV_CLOSE': np.roll(close_prices, 1),
            'OPEN_PRICE': open_prices,
            'HIGH_PRICE': high_prices,
            'LOW_PRICE': low_prices,
            'CLOSE_PRICE': close_prices,
            'TTL_TRD_QNTY': volumes.astype(int),
            'DELIV_QTY': deliv_qty.astype(int),
            'DELIV_PER': deliv_per,
            'NO_OF_TRADES': no_of_trades
        })
        
        df['PREV_CLOSE'].iloc[0] = df['CLOSE_PRICE'].iloc[0]
        df.set_index('DATE1', inplace=True)
        
        data_dict[symbol] = df
    
    return data_dict

def main():
    """Main function to demonstrate the stage analysis"""
    print("Stan Weinstein's Stage Analysis with Wyckoff Method")
    print("=" * 50)
    
    # Generate dummy data for testing
    symbols = ['STOCK_A', 'STOCK_B', 'STOCK_C', 'STOCK_D', 'STOCK_E']
    print(f"Generating dummy data for {len(symbols)} stocks...")
    
    data_dict = generate_dummy_data(symbols)
    
    # Initialize analyzer
    analyzer = StageAnalyzer()
    
    # Analyze stocks
    print("Analyzing stocks for Stage 1 and Stage 2 conditions...")
    recent_stage2, current_stage1 = analyzer.analyze_stocks(data_dict, lookback_days=5)
    
    # Display results
    print(f"\n📈 RECENT STAGE 2 ENTRIES (Last 5 days): {len(recent_stage2)} stocks")
    print("-" * 60)
    for entry in recent_stage2:
        print(f"Symbol: {entry['symbol']}")
        print(f"  Entry Date: {entry['entry_date'].strftime('%Y-%m-%d')}")
        print(f"  Entry Price: ${entry['entry_price']:.2f}")
        print(f"  Current Price: ${entry['current_price']:.2f}")
        print(f"  MA200: ${entry['ma200']:.2f}")
        print(f"  Volume Ratio: {entry['volume_ratio']:.2f}")
        print()
    
    print(f"🔄 CURRENT STAGE 1 STOCKS (Accumulation): {len(current_stage1)} stocks")
    print("-" * 60)
    for stock in current_stage1:
        print(f"Symbol: {stock['symbol']}")
        print(f"  Current Price: ${stock['current_price']:.2f}")
        print(f"  MA200: ${stock['ma200']:.2f}")
        print(f"  Price vs MA: {stock['price_vs_ma']:.2f}%")
        print(f"  Volatility: {stock['volatility']:.2f}%")
        print(f"  Volume Ratio: {stock['volume_ratio']:.2f}")
        print()

if __name__ == "__main__":
    main()
