"""
Complete Stage Analysis with MySQL Data Integration
Combines MySQL data loading with <PERSON>'s Stage Analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from mysql_data_loader import MySQLDataLoader, load_data_for_stage_analysis
from stage_analysis import Stage<PERSON><PERSON><PERSON>zer

def analyze_stocks_from_mysql(symbols, mysql_config, recent_date=None, lookback_days=5, limit=252):
    """
    Complete pipeline: Load data from MySQL and perform stage analysis
    
    Args:
        symbols (list): List of stock symbols to analyze
        mysql_config (dict): MySQL connection configuration
        recent_date (str or datetime): Recent date to load data up to
        lookback_days (int): Days to look back for recent Stage 2 entries
        limit (int): Number of records to fetch per symbol
    
    Returns:
        tuple: (recent_stage2_entries, current_stage1_stocks, stock_data)
    """
    
    print("🔍 MYSQL STAGE ANALYSIS PIPELINE")
    print("=" * 50)
    
    # Step 1: Load data from MySQL
    print("Step 1: Loading data from MySQL...")
    stock_data = load_data_for_stage_analysis(symbols, mysql_config, recent_date, limit)
    
    if not stock_data:
        print("❌ No data loaded from MySQL")
        return [], [], {}
    
    # Step 2: Perform stage analysis
    print(f"\nStep 2: Performing stage analysis on {len(stock_data)} stocks...")
    analyzer = StageAnalyzer()
    recent_stage2, current_stage1 = analyzer.analyze_stocks(stock_data, lookback_days)
    
    return recent_stage2, current_stage1, stock_data

def display_mysql_analysis_results(recent_stage2, current_stage1, stock_data):
    """Display detailed analysis results from MySQL data"""
    
    print(f"\n📈 RECENT STAGE 2 ENTRIES: {len(recent_stage2)} stocks")
    print("-" * 60)
    
    if recent_stage2:
        # Sort by entry date (most recent first)
        recent_stage2.sort(key=lambda x: x['entry_date'], reverse=True)
        
        for i, entry in enumerate(recent_stage2, 1):
            gain_loss = ((entry['current_price'] - entry['entry_price']) / entry['entry_price']) * 100
            
            # Get additional data from the original DataFrame
            symbol_df = stock_data[entry['symbol']]
            latest_data = symbol_df.iloc[-1]
            
            print(f"{i}. {entry['symbol']}")
            print(f"   📅 Entry Date: {entry['entry_date'].strftime('%Y-%m-%d')}")
            print(f"   💰 Entry Price: ₹{entry['entry_price']:.2f}")
            print(f"   📊 Current Price: ₹{entry['current_price']:.2f}")
            print(f"   📈 Gain/Loss: {gain_loss:+.2f}%")
            print(f"   📉 MA200: ₹{entry['ma200']:.2f}")
            print(f"   📊 Volume Ratio: {entry['volume_ratio']:.2f}x")
            print(f"   🎯 Price above MA: {((entry['current_price'] - entry['ma200']) / entry['ma200'] * 100):+.2f}%")
            print(f"   📦 Delivery %: {latest_data['DELIV_PER']:.1f}%")
            print(f"   🔢 No. of Trades: {latest_data['NO_OF_TRADES']:,}")
            print()
    else:
        print("   No recent Stage 2 entries found.")
        print()
    
    print(f"🔄 CURRENT STAGE 1 STOCKS (Accumulation Phase): {len(current_stage1)} stocks")
    print("-" * 60)
    
    if current_stage1:
        # Sort by how close to MA200 (best accumulation candidates first)
        current_stage1.sort(key=lambda x: abs(x['price_vs_ma']))
        
        for i, stock in enumerate(current_stage1, 1):
            # Get additional data from the original DataFrame
            symbol_df = stock_data[stock['symbol']]
            latest_data = symbol_df.iloc[-1]
            
            print(f"{i}. {stock['symbol']}")
            print(f"   💰 Current Price: ₹{stock['current_price']:.2f}")
            print(f"   📉 MA200: ₹{stock['ma200']:.2f}")
            print(f"   📊 Price vs MA: {stock['price_vs_ma']:+.2f}%")
            print(f"   📈 Volatility: {stock['volatility']:.2f}%")
            print(f"   📊 Volume Ratio: {stock['volume_ratio']:.2f}x")
            print(f"   📦 Delivery %: {latest_data['DELIV_PER']:.1f}%")
            print(f"   🔢 No. of Trades: {latest_data['NO_OF_TRADES']:,}")
            
            # Accumulation quality score
            quality_score = (
                (5 - abs(stock['price_vs_ma'])) * 0.4 +  # Closer to MA is better
                (3 - stock['volatility']) * 0.3 +         # Lower volatility is better
                (2 - abs(stock['volume_ratio'] - 1)) * 0.3  # Volume near average is better
            )
            quality_score = max(0, min(10, quality_score))
            
            print(f"   ⭐ Accumulation Quality: {quality_score:.1f}/10")
            print()
    else:
        print("   No stocks currently in Stage 1 accumulation phase.")
        print()

def get_top_stocks_by_delivery(mysql_config, min_delivery_pct=50, limit=20):
    """
    Get stocks with high delivery percentage for analysis
    
    Args:
        mysql_config (dict): MySQL connection configuration
        min_delivery_pct (float): Minimum delivery percentage
        limit (int): Number of stocks to return
    
    Returns:
        list: List of stock symbols with high delivery percentage
    """
    loader = MySQLDataLoader(mysql_config)
    
    try:
        if not loader.connect():
            return []
        
        # Query to get stocks with high delivery percentage
        query = """
        SELECT SYMBOL, AVG(DELIV_PER) as avg_delivery
        FROM `analysis` 
        WHERE DATE1 >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY SYMBOL
        HAVING avg_delivery >= %s
        ORDER BY avg_delivery DESC
        LIMIT %s
        """
        
        loader.cursor.execute(query, (min_delivery_pct, limit))
        results = loader.cursor.fetchall()
        
        symbols = [row[0] for row in results]
        print(f"📦 Found {len(symbols)} stocks with delivery % >= {min_delivery_pct}%")
        
        return symbols
        
    except Exception as e:
        print(f"❌ Error fetching high delivery stocks: {e}")
        return []
    finally:
        loader.disconnect()

def main():
    """Main function demonstrating MySQL Stage Analysis"""
    
    # MySQL configuration
    mysql_config = {
        'user': 'root',
        'password': 'rootroot',
        'host': 'localhost',
        'database': 'delivery',
        'raise_on_warnings': True
    }
    
    print("🚀 MYSQL STAGE ANALYSIS SYSTEM")
    print("=" * 50)
    
    # Option 1: Analyze specific symbols
    print("\n📋 Option 1: Analyze specific symbols")
    specific_symbols = ['RELIANCE', 'TCS', 'INFY', 'HDFC', 'ICICI']
    
    try:
        recent_stage2, current_stage1, stock_data = analyze_stocks_from_mysql(
            symbols=specific_symbols,
            mysql_config=mysql_config,
            recent_date=None,  # Use current date
            lookback_days=5,
            limit=252  # ~1 year of data
        )
        
        display_mysql_analysis_results(recent_stage2, current_stage1, stock_data)
        
    except Exception as e:
        print(f"❌ Error in specific symbols analysis: {e}")
    
    print("\n" + "="*70)
    
    # Option 2: Analyze stocks with high delivery percentage
    print("\n📦 Option 2: Analyze high delivery percentage stocks")
    
    try:
        high_delivery_symbols = get_top_stocks_by_delivery(
            mysql_config, 
            min_delivery_pct=60, 
            limit=10
        )
        
        if high_delivery_symbols:
            recent_stage2_hd, current_stage1_hd, stock_data_hd = analyze_stocks_from_mysql(
                symbols=high_delivery_symbols,
                mysql_config=mysql_config,
                recent_date=None,
                lookback_days=5,
                limit=252
            )
            
            display_mysql_analysis_results(recent_stage2_hd, current_stage1_hd, stock_data_hd)
        
    except Exception as e:
        print(f"❌ Error in high delivery analysis: {e}")
    
    # Summary
    print("\n" + "="*70)
    print("📊 ANALYSIS COMPLETE")
    print("-" * 30)
    print("✅ MySQL data loading integrated")
    print("✅ Stage analysis performed")
    print("✅ Results displayed with delivery data")
    print("\n💡 Next steps:")
    print("   - Modify symbols list in the script")
    print("   - Adjust MySQL configuration")
    print("   - Customize analysis parameters")
    print("   - Set up automated alerts")

if __name__ == "__main__":
    main()
