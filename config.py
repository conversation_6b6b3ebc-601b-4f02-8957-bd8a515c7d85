"""
Configuration file for Stage Analysis
Modify these parameters according to your requirements
"""

# Stage Analysis Parameters
STAGE_ANALYSIS_CONFIG = {
    # Moving Average Settings
    'ma_period': 200,                    # 200-day moving average (standard for Stage Analysis)
    'ma_slope_period': 5,                # Period for calculating MA slope
    
    # Volatility Settings
    'volatility_window': 20,             # Window for volatility calculations
    'atr_period': 14,                    # Average True Range period
    
    # Volume Settings
    'volume_window': 20,                 # Window for volume moving average
    'high_volume_threshold': 1.5,        # Volume ratio threshold for high volume
    'stage2_volume_threshold': 1.2,      # Minimum volume ratio for Stage 2
    
    # Stage 1 (Accumulation) Criteria
    'stage1_price_tolerance': 5,         # Price within ±5% of MA200
    'stage1_max_breakout': 10,           # Max price change % in 5 days
    'stage1_max_volume_ratio': 1.5,      # Max volume ratio
    
    # Stage 2 (Advancing) Criteria
    'stage2_min_price_above_ma': 2,      # Minimum % above MA200
    'stage2_min_volume_ratio': 1.2,      # Minimum volume ratio
    
    # Analysis Settings
    'lookback_days': 5,                  # Days to look back for recent Stage 2 entries
    'min_data_points': 250,              # Minimum data points required (MA period + buffer)
}

# Data Column Mapping
# Modify these if your DataFrame has different column names
COLUMN_MAPPING = {
    'date': 'DATE1',
    'prev_close': 'PREV_CLOSE',
    'open': 'OPEN_PRICE',
    'high': 'HIGH_PRICE',
    'low': 'LOW_PRICE',
    'close': 'CLOSE_PRICE',
    'volume': 'TTL_TRD_QNTY',
    'delivery_qty': 'DELIV_QTY',
    'delivery_per': 'DELIV_PER',
    'num_trades': 'NO_OF_TRADES'
}

# File Paths (modify according to your setup)
DATA_PATHS = {
    'data_directory': 'data/',           # Directory containing stock data files
    'output_directory': 'output/',       # Directory for saving results
    'log_directory': 'logs/',            # Directory for log files
}

# Stock Lists (modify with your actual stock symbols)
STOCK_LISTS = {
    'nifty50': [
        'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'HINDUNILVR',
        'ICICIBANK', 'KOTAKBANK', 'HDFC', 'BHARTIARTL', 'ITC',
        'SBIN', 'BAJFINANCE', 'LICI', 'LT', 'HCLTECH',
        'MARUTI', 'SUNPHARMA', 'TITAN', 'ULTRACEMCO', 'ONGC',
        'TATAMOTORS', 'AXISBANK', 'NESTLEIND', 'NTPC', 'POWERGRID',
        'BAJAJFINSV', 'M&M', 'WIPRO', 'JSWSTEEL', 'LTIM',
        'TECHM', 'COALINDIA', 'HINDALCO', 'TATASTEEL', 'GRASIM',
        'ADANIENT', 'INDUSINDBK', 'CIPLA', 'EICHERMOT', 'HEROMOTOCO',
        'BRITANNIA', 'BPCL', 'APOLLOHOSP', 'DIVISLAB', 'DRREDDY',
        'TRENT', 'BAJAJ-AUTO', 'ASIANPAINT', 'ADANIPORTS', 'UPL'
    ],
    
    'custom_watchlist': [
        # Add your custom stock symbols here
        'STOCK1', 'STOCK2', 'STOCK3'
    ]
}

# Display Settings
DISPLAY_CONFIG = {
    'currency_symbol': '₹',              # Currency symbol for display
    'decimal_places': 2,                 # Decimal places for price display
    'percentage_decimal_places': 2,      # Decimal places for percentages
    'max_stocks_display': 20,            # Maximum stocks to display in results
}

# Alert Settings
ALERT_CONFIG = {
    'enable_alerts': False,              # Enable/disable alerts
    'stage2_entry_alert': True,          # Alert on new Stage 2 entries
    'stage1_accumulation_alert': False,  # Alert on Stage 1 accumulation
    'email_alerts': False,               # Enable email alerts (requires setup)
    'email_recipients': [],              # List of email addresses
}

# Visualization Settings
VISUALIZATION_CONFIG = {
    'figure_size': (15, 12),             # Default figure size for plots
    'dpi': 300,                          # DPI for saved plots
    'save_plots': True,                  # Automatically save plots
    'plot_format': 'png',                # Plot file format
    'color_scheme': {
        'stage1': 'orange',              # Color for Stage 1 periods
        'stage2': 'green',               # Color for Stage 2 periods
        'stage2_entry': 'red',           # Color for Stage 2 entry points
        'ma200': 'blue',                 # Color for MA200 line
        'price': 'black',                # Color for price line
        'volume': 'lightblue',           # Color for volume bars
        'high_volume': 'red',            # Color for high volume bars
    }
}

# Performance Settings
PERFORMANCE_CONFIG = {
    'parallel_processing': False,        # Enable parallel processing (for large datasets)
    'max_workers': 4,                    # Maximum worker threads
    'chunk_size': 100,                   # Chunk size for batch processing
    'cache_indicators': True,            # Cache calculated indicators
}

# Logging Settings
LOGGING_CONFIG = {
    'log_level': 'INFO',                 # Log level (DEBUG, INFO, WARNING, ERROR)
    'log_to_file': True,                 # Save logs to file
    'log_filename': 'stage_analysis.log', # Log filename
    'max_log_size': 10,                  # Max log file size in MB
    'backup_count': 5,                   # Number of backup log files
}

def get_config():
    """Get the complete configuration dictionary"""
    return {
        'stage_analysis': STAGE_ANALYSIS_CONFIG,
        'columns': COLUMN_MAPPING,
        'paths': DATA_PATHS,
        'stocks': STOCK_LISTS,
        'display': DISPLAY_CONFIG,
        'alerts': ALERT_CONFIG,
        'visualization': VISUALIZATION_CONFIG,
        'performance': PERFORMANCE_CONFIG,
        'logging': LOGGING_CONFIG,
    }

def validate_config():
    """Validate configuration parameters"""
    config = get_config()
    
    # Validate stage analysis parameters
    stage_config = config['stage_analysis']
    
    assert stage_config['ma_period'] > 0, "MA period must be positive"
    assert stage_config['lookback_days'] > 0, "Lookback days must be positive"
    assert 0 < stage_config['stage2_volume_threshold'] < stage_config['high_volume_threshold'], \
           "Volume thresholds must be properly ordered"
    
    print("✅ Configuration validation passed")
    return True

if __name__ == "__main__":
    # Test configuration
    config = get_config()
    validate_config()
    
    print("📋 STAGE ANALYSIS CONFIGURATION")
    print("=" * 50)
    
    for section, params in config.items():
        print(f"\n{section.upper()}:")
        for key, value in params.items():
            print(f"  {key}: {value}")
    
    print(f"\n✅ Configuration loaded successfully!")
    print(f"Total stock symbols in Nifty 50: {len(STOCK_LISTS['nifty50'])}")
    print(f"MA Period: {STAGE_ANALYSIS_CONFIG['ma_period']} days")
    print(f"Lookback Period: {STAGE_ANALYSIS_CONFIG['lookback_days']} days")
