"""
Complete Stage Analysis System with MySQL Integration
Combines <PERSON>'s Stage Analysis with Wyckoff Method and MySQL data loading
All-in-one script for stock market stage analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

try:
    import mysql.connector
    from mysql.connector import Error
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False
    print("MySQL connector not available. Install with: pip install mysql-connector-python")

class StageAnalyzer:
    """
    <PERSON>'s Stage Analysis with Wyckoff Method principles
    Adapted for daily data with 200-day moving average
    """
    
    def __init__(self, ma_period=200, volatility_window=20, volume_window=20):
        self.ma_period = ma_period
        self.volatility_window = volatility_window
        self.volume_window = volume_window
    
    def calculate_indicators(self, df):
        """Calculate technical indicators needed for stage analysis"""
        df = df.copy()
        
        # 200-day moving average
        df['MA200'] = df['CLOSE_PRICE'].rolling(window=self.ma_period).mean()
        
        # MA slope (trend direction)
        df['MA200_slope'] = df['MA200'].diff(5) / 5  # 5-day slope
        
        # Price relative to MA200
        df['price_vs_ma'] = (df['CLOSE_PRICE'] - df['MA200']) / df['MA200'] * 100
        
        # Volatility (Average True Range percentage)
        df['high_low'] = df['HIGH_PRICE'] - df['LOW_PRICE']
        df['high_close'] = abs(df['HIGH_PRICE'] - df['CLOSE_PRICE'].shift(1))
        df['low_close'] = abs(df['LOW_PRICE'] - df['CLOSE_PRICE'].shift(1))
        df['true_range'] = df[['high_low', 'high_close', 'low_close']].max(axis=1)
        df['atr'] = df['true_range'].rolling(window=14).mean()
        df['volatility_pct'] = (df['atr'] / df['CLOSE_PRICE']) * 100
        
        # Volume indicators
        df['volume_ma'] = df['TTL_TRD_QNTY'].rolling(window=self.volume_window).mean()
        df['volume_ratio'] = df['TTL_TRD_QNTY'] / df['volume_ma']
        
        # Price momentum
        df['price_change_5d'] = df['CLOSE_PRICE'].pct_change(5) * 100
        df['price_change_10d'] = df['CLOSE_PRICE'].pct_change(10) * 100
        
        return df
    
    def detect_stage1_accumulation(self, df):
        """
        Detect Stage 1 (Accumulation) conditions:
        - Price moving sideways around 200MA
        - Low volatility
        - Decreasing or stable volume
        - No strong breakouts
        """
        conditions = (
            # Price near 200MA (within ±5%)
            (abs(df['price_vs_ma']) <= 5) &
            
            # Low volatility (below average)
            (df['volatility_pct'] <= df['volatility_pct'].rolling(50).mean()) &
            
            # MA200 is relatively flat (slope close to zero)
            (abs(df['MA200_slope']) <= df['CLOSE_PRICE'] * 0.001) &
            
            # No strong recent breakouts (price change < 10% in 5 days)
            (abs(df['price_change_5d']) <= 10) &
            
            # Volume not significantly elevated
            (df['volume_ratio'] <= 1.5)
        )
        
        return conditions
    
    def detect_stage2_advancing(self, df):
        """
        Detect Stage 2 (Advancing) conditions:
        - Price breaking above 200MA
        - Rising volume
        - 200MA turning upward
        """
        conditions = (
            # Price above 200MA
            (df['CLOSE_PRICE'] > df['MA200']) &
            
            # Price significantly above MA (at least 2%)
            (df['price_vs_ma'] >= 2) &
            
            # MA200 slope is positive (upward trend)
            (df['MA200_slope'] > 0) &
            
            # Volume above average (confirmation)
            (df['volume_ratio'] >= 1.2) &
            
            # Positive momentum
            (df['price_change_5d'] > 0)
        )
        
        return conditions
    
    def detect_stage_transitions(self, df):
        """Detect transitions from Stage 1 to Stage 2"""
        df = df.copy()
        
        # Calculate stage conditions
        df['stage1'] = self.detect_stage1_accumulation(df)
        df['stage2'] = self.detect_stage2_advancing(df)
        
        # Detect Stage 1 to Stage 2 transitions
        df['stage1_prev'] = df['stage1'].shift(1).fillna(False)
        df['stage2_entry'] = ((~df['stage1_prev']) & df['stage2']) | (df['stage1_prev'] & df['stage2'])
        
        return df
    
    def analyze_stocks(self, data_dict, lookback_days=5):
        """
        Analyze multiple stocks and return Stage 1 and recent Stage 2 entries
        
        Args:
            data_dict: Dictionary with stock symbols as keys and DataFrames as values
            lookback_days: Number of days to look back for recent Stage 2 entries
        
        Returns:
            tuple: (recent_stage2_entries, current_stage1_stocks)
        """
        recent_stage2_entries = []
        current_stage1_stocks = []
        
        for symbol, df in data_dict.items():
            if len(df) < self.ma_period + 50:  # Need enough data
                continue
                
            # Calculate indicators and detect stages
            df_analyzed = self.calculate_indicators(df)
            df_analyzed = self.detect_stage_transitions(df_analyzed)
            
            # Get recent data (last lookback_days)
            recent_data = df_analyzed.tail(lookback_days)
            latest_data = df_analyzed.iloc[-1]
            
            # Check for recent Stage 2 entries
            if recent_data['stage2_entry'].any():
                stage2_entry_date = recent_data[recent_data['stage2_entry']].index[-1]
                recent_stage2_entries.append({
                    'symbol': symbol,
                    'entry_date': stage2_entry_date,
                    'entry_price': df_analyzed.loc[stage2_entry_date, 'CLOSE_PRICE'],
                    'current_price': latest_data['CLOSE_PRICE'],
                    'ma200': latest_data['MA200'],
                    'volume_ratio': latest_data['volume_ratio']
                })
            
            # Check for current Stage 1 stocks
            if latest_data['stage1']:
                current_stage1_stocks.append({
                    'symbol': symbol,
                    'current_price': latest_data['CLOSE_PRICE'],
                    'ma200': latest_data['MA200'],
                    'price_vs_ma': latest_data['price_vs_ma'],
                    'volatility': latest_data['volatility_pct'],
                    'volume_ratio': latest_data['volume_ratio']
                })
        
        return recent_stage2_entries, current_stage1_stocks

class MySQLDataLoader:
    """
    MySQL data loader for stock market data
    """
    
    def __init__(self, config):
        """
        Initialize MySQL connection
        
        Args:
            config (dict): MySQL connection configuration
        """
        if not MYSQL_AVAILABLE:
            raise ImportError("mysql-connector-python is required. Install with: pip install mysql-connector-python")
        
        self.config = config
        self.connection = None
        self.cursor = None
        
        # Column names for the DataFrame
        self.column_names = [
            'PREV_CLOSE', 'OPEN_PRICE', 'CLOSE_PRICE', 'HIGH_PRICE', 'LOW_PRICE',
            'DELIV_PER', 'DELIV_QTY', 'TTL_TRD_QNTY', 'NO_OF_TRADES', 'DATE1'
        ]
    
    def connect(self):
        """Establish database connection"""
        try:
            self.connection = mysql.connector.connect(**self.config)
            self.cursor = self.connection.cursor()
            print("✅ MySQL connection established successfully")
            return True
        except Error as e:
            print(f"❌ Error connecting to MySQL: {e}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔌 MySQL connection closed")
    
    def load_stock_data(self, symbol, recent_date=None, limit=252):
        """
        Load stock data for a single symbol
        
        Args:
            symbol (str): Stock symbol
            recent_date (str or datetime): Recent date to load data up to
            limit (int): Number of records to fetch (default: 252 for ~1 year)
        
        Returns:
            pandas.DataFrame: Stock data with DATE1 as index
        """
        if not self.connection:
            if not self.connect():
                return None
        
        # Set default recent_date to today if not provided
        if recent_date is None:
            recent_date = datetime.now().strftime('%Y-%m-%d')
        elif isinstance(recent_date, datetime):
            recent_date = recent_date.strftime('%Y-%m-%d')
        
        # SQL query to fetch stock data
        query = """
        SELECT PREV_CLOSE, OPEN_PRICE, CLOSE_PRICE, HIGH_PRICE, LOW_PRICE,
               DELIV_PER, DELIV_QTY, TTL_TRD_QNTY, NO_OF_TRADES, DATE1 
        FROM `analysis` 
        WHERE SYMBOL = %s AND DATE1 <= %s 
        ORDER BY DATE1 DESC 
        LIMIT %s
        """
        
        try:
            values = (symbol, recent_date, limit)
            self.cursor.execute(query, values)
            rows = self.cursor.fetchall()
            
            if not rows:
                print(f"⚠️  No data found for symbol: {symbol}")
                return None
            
            # Create DataFrame
            df = pd.DataFrame(rows, columns=self.column_names)
            
            # Convert DATE1 to datetime and set as index
            df['DATE1'] = pd.to_datetime(df['DATE1'])
            df.set_index('DATE1', inplace=True)
            
            # Sort by date (ascending order for analysis)
            df.sort_index(inplace=True)
            
            # Convert numeric columns to appropriate types
            numeric_columns = [
                'PREV_CLOSE', 'OPEN_PRICE', 'CLOSE_PRICE', 'HIGH_PRICE', 'LOW_PRICE',
                'DELIV_PER', 'DELIV_QTY', 'TTL_TRD_QNTY', 'NO_OF_TRADES'
            ]
            
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            print(f"✅ Loaded {len(df)} records for {symbol} (from {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')})")
            
            return df
            
        except Error as e:
            print(f"❌ Error loading data for {symbol}: {e}")
            return None
    
    def load_multiple_stocks(self, symbols, recent_date=None, limit=252):
        """
        Load data for multiple stock symbols
        
        Args:
            symbols (list): List of stock symbols
            recent_date (str or datetime): Recent date to load data up to
            limit (int): Number of records to fetch per symbol
        
        Returns:
            dict: Dictionary with symbol as key and DataFrame as value
        """
        if not self.connection:
            if not self.connect():
                return {}
        
        stock_data = {}
        successful_loads = 0
        
        print(f"📊 Loading data for {len(symbols)} stocks...")
        
        for i, symbol in enumerate(symbols, 1):
            print(f"Loading {i}/{len(symbols)}: {symbol}...", end=" ")
            
            df = self.load_stock_data(symbol, recent_date, limit)
            
            if df is not None and len(df) >= 200:  # Ensure sufficient data for analysis
                stock_data[symbol] = df
                successful_loads += 1
                print("✅")
            else:
                print("❌ (insufficient data)")
        
        print(f"\n📈 Successfully loaded {successful_loads}/{len(symbols)} stocks")
        return stock_data

    def get_available_symbols(self, limit=None):
        """
        Get list of available stock symbols in the database

        Args:
            limit (int): Limit number of symbols returned

        Returns:
            list: List of available stock symbols
        """
        if not self.connection:
            if not self.connect():
                return []

        query = "SELECT DISTINCT SYMBOL FROM `analysis` ORDER BY SYMBOL"
        if limit:
            query += f" LIMIT {limit}"

        try:
            self.cursor.execute(query)
            symbols = [row[0] for row in self.cursor.fetchall()]
            print(f"📋 Found {len(symbols)} unique symbols in database")
            return symbols
        except Error as e:
            print(f"❌ Error fetching symbols: {e}")
            return []

    def get_date_range(self, symbol=None):
        """
        Get the date range of available data

        Args:
            symbol (str): Specific symbol to check (optional)

        Returns:
            tuple: (min_date, max_date)
        """
        if not self.connection:
            if not self.connect():
                return None, None

        if symbol:
            query = "SELECT MIN(DATE1), MAX(DATE1) FROM `analysis` WHERE SYMBOL = %s"
            values = (symbol,)
        else:
            query = "SELECT MIN(DATE1), MAX(DATE1) FROM `analysis`"
            values = ()

        try:
            self.cursor.execute(query, values)
            result = self.cursor.fetchone()
            min_date, max_date = result if result else (None, None)

            if min_date and max_date:
                print(f"📅 Data range: {min_date} to {max_date}")

            return min_date, max_date
        except Error as e:
            print(f"❌ Error fetching date range: {e}")
            return None, None

def analyze_stocks_from_mysql(symbols, mysql_config, recent_date=None, lookback_days=5, limit=252):
    """
    Complete pipeline: Load data from MySQL and perform stage analysis

    Args:
        symbols (list): List of stock symbols to analyze
        mysql_config (dict): MySQL connection configuration
        recent_date (str or datetime): Recent date to load data up to
        lookback_days (int): Days to look back for recent Stage 2 entries
        limit (int): Number of records to fetch per symbol

    Returns:
        tuple: (recent_stage2_entries, current_stage1_stocks, stock_data)
    """

    print("🔍 MYSQL STAGE ANALYSIS PIPELINE")
    print("=" * 50)

    # Step 1: Load data from MySQL
    print("Step 1: Loading data from MySQL...")
    loader = MySQLDataLoader(mysql_config)

    try:
        stock_data = loader.load_multiple_stocks(symbols, recent_date, limit)

        if not stock_data:
            print("❌ No data loaded from MySQL")
            return [], [], {}

        # Step 2: Perform stage analysis
        print(f"\nStep 2: Performing stage analysis on {len(stock_data)} stocks...")
        analyzer = StageAnalyzer()
        recent_stage2, current_stage1 = analyzer.analyze_stocks(stock_data, lookback_days)

        return recent_stage2, current_stage1, stock_data

    finally:
        loader.disconnect()

def display_analysis_results(recent_stage2, current_stage1, stock_data):
    """Display detailed analysis results"""

    print(f"\n📈 RECENT STAGE 2 ENTRIES: {len(recent_stage2)} stocks")
    print("-" * 60)

    if recent_stage2:
        # Sort by entry date (most recent first)
        recent_stage2.sort(key=lambda x: x['entry_date'], reverse=True)

        for i, entry in enumerate(recent_stage2, 1):
            gain_loss = ((entry['current_price'] - entry['entry_price']) / entry['entry_price']) * 100

            # Get additional data from the original DataFrame
            if entry['symbol'] in stock_data:
                symbol_df = stock_data[entry['symbol']]
                latest_data = symbol_df.iloc[-1]
                delivery_pct = latest_data['DELIV_PER']
                num_trades = latest_data['NO_OF_TRADES']
            else:
                delivery_pct = 0
                num_trades = 0

            print(f"{i}. {entry['symbol']}")
            print(f"   📅 Entry Date: {entry['entry_date'].strftime('%Y-%m-%d')}")
            print(f"   💰 Entry Price: ₹{entry['entry_price']:.2f}")
            print(f"   📊 Current Price: ₹{entry['current_price']:.2f}")
            print(f"   📈 Gain/Loss: {gain_loss:+.2f}%")
            print(f"   📉 MA200: ₹{entry['ma200']:.2f}")
            print(f"   📊 Volume Ratio: {entry['volume_ratio']:.2f}x")
            print(f"   🎯 Price above MA: {((entry['current_price'] - entry['ma200']) / entry['ma200'] * 100):+.2f}%")
            print(f"   📦 Delivery %: {delivery_pct:.1f}%")
            print(f"   🔢 No. of Trades: {num_trades:,}")
            print()
    else:
        print("   No recent Stage 2 entries found.")
        print()

    print(f"🔄 CURRENT STAGE 1 STOCKS (Accumulation Phase): {len(current_stage1)} stocks")
    print("-" * 60)

    if current_stage1:
        # Sort by how close to MA200 (best accumulation candidates first)
        current_stage1.sort(key=lambda x: abs(x['price_vs_ma']))

        for i, stock in enumerate(current_stage1, 1):
            # Get additional data from the original DataFrame
            if stock['symbol'] in stock_data:
                symbol_df = stock_data[stock['symbol']]
                latest_data = symbol_df.iloc[-1]
                delivery_pct = latest_data['DELIV_PER']
                num_trades = latest_data['NO_OF_TRADES']
            else:
                delivery_pct = 0
                num_trades = 0

            print(f"{i}. {stock['symbol']}")
            print(f"   💰 Current Price: ₹{stock['current_price']:.2f}")
            print(f"   📉 MA200: ₹{stock['ma200']:.2f}")
            print(f"   📊 Price vs MA: {stock['price_vs_ma']:+.2f}%")
            print(f"   📈 Volatility: {stock['volatility']:.2f}%")
            print(f"   📊 Volume Ratio: {stock['volume_ratio']:.2f}x")
            print(f"   📦 Delivery %: {delivery_pct:.1f}%")
            print(f"   🔢 No. of Trades: {num_trades:,}")

            # Accumulation quality score
            quality_score = (
                (5 - abs(stock['price_vs_ma'])) * 0.4 +  # Closer to MA is better
                (3 - stock['volatility']) * 0.3 +         # Lower volatility is better
                (2 - abs(stock['volume_ratio'] - 1)) * 0.3  # Volume near average is better
            )
            quality_score = max(0, min(10, quality_score))

            print(f"   ⭐ Accumulation Quality: {quality_score:.1f}/10")
            print()
    else:
        print("   No stocks currently in Stage 1 accumulation phase.")
        print()

def get_high_delivery_stocks(mysql_config, min_delivery_pct=50, limit=20):
    """
    Get stocks with high delivery percentage for analysis

    Args:
        mysql_config (dict): MySQL connection configuration
        min_delivery_pct (float): Minimum delivery percentage
        limit (int): Number of stocks to return

    Returns:
        list: List of stock symbols with high delivery percentage
    """
    loader = MySQLDataLoader(mysql_config)

    try:
        if not loader.connect():
            return []

        # Query to get stocks with high delivery percentage
        query = """
        SELECT SYMBOL, AVG(DELIV_PER) as avg_delivery
        FROM `analysis`
        WHERE DATE1 >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY SYMBOL
        HAVING avg_delivery >= %s
        ORDER BY avg_delivery DESC
        LIMIT %s
        """

        loader.cursor.execute(query, (min_delivery_pct, limit))
        results = loader.cursor.fetchall()

        symbols = [row[0] for row in results]
        print(f"📦 Found {len(symbols)} stocks with delivery % >= {min_delivery_pct}%")

        return symbols

    except Exception as e:
        print(f"❌ Error fetching high delivery stocks: {e}")
        return []
    finally:
        loader.disconnect()

def generate_dummy_data(symbols, start_date='2023-01-01', end_date='2024-12-31'):
    """Generate dummy OHLCV data for testing when MySQL is not available"""
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    # Remove weekends (assuming stock market data)
    date_range = date_range[date_range.weekday < 5]

    data_dict = {}

    for symbol in symbols:
        np.random.seed(hash(symbol) % 2**32)  # Consistent random data per symbol

        n_days = len(date_range)

        # Generate base price trend
        trend = np.cumsum(np.random.normal(0.001, 0.02, n_days))
        base_price = 100 * np.exp(trend)

        # Add some cyclical patterns for stage analysis
        cycle = 10 * np.sin(np.arange(n_days) * 2 * np.pi / 200)
        base_price += cycle

        # Generate OHLCV data
        volatility = np.random.uniform(0.01, 0.03, n_days)

        close_prices = base_price
        open_prices = close_prices * (1 + np.random.normal(0, volatility/2))
        high_prices = np.maximum(open_prices, close_prices) * (1 + np.random.uniform(0, volatility))
        low_prices = np.minimum(open_prices, close_prices) * (1 - np.random.uniform(0, volatility))

        # Volume (correlated with price movements)
        price_changes = np.abs(np.diff(close_prices, prepend=close_prices[0]))
        base_volume = 1000000
        volumes = base_volume * (1 + price_changes / np.mean(price_changes)) * np.random.uniform(0.5, 2.0, n_days)

        # Delivery data
        deliv_per = np.random.uniform(20, 80, n_days)
        deliv_qty = volumes * deliv_per / 100

        # Number of trades
        no_of_trades = (volumes / 1000 * np.random.uniform(0.8, 1.2, n_days)).astype(int)

        df = pd.DataFrame({
            'DATE1': date_range,
            'PREV_CLOSE': np.roll(close_prices, 1),
            'OPEN_PRICE': open_prices,
            'HIGH_PRICE': high_prices,
            'LOW_PRICE': low_prices,
            'CLOSE_PRICE': close_prices,
            'TTL_TRD_QNTY': volumes.astype(int),
            'DELIV_QTY': deliv_qty.astype(int),
            'DELIV_PER': deliv_per,
            'NO_OF_TRADES': no_of_trades
        })

        df['PREV_CLOSE'].iloc[0] = df['CLOSE_PRICE'].iloc[0]
        df.set_index('DATE1', inplace=True)

        data_dict[symbol] = df

    return data_dict

def analyze_single_stock(symbol, mysql_config=None, recent_date=None, limit=252):
    """
    Analyze a single stock in detail

    Args:
        symbol (str): Stock symbol
        mysql_config (dict): MySQL configuration (optional)
        recent_date (str): Recent date to load data up to
        limit (int): Number of records to fetch

    Returns:
        dict: Detailed analysis results
    """

    print(f"📊 DETAILED ANALYSIS: {symbol}")
    print("=" * 50)

    # Load data
    if mysql_config and MYSQL_AVAILABLE:
        loader = MySQLDataLoader(mysql_config)
        try:
            df = loader.load_stock_data(symbol, recent_date, limit)
        finally:
            loader.disconnect()
    else:
        # Use dummy data for testing
        dummy_data = generate_dummy_data([symbol])
        df = dummy_data[symbol] if symbol in dummy_data else None

    if df is None or len(df) < 200:
        print(f"❌ Insufficient data for {symbol}")
        return None

    # Perform analysis
    analyzer = StageAnalyzer()
    df_analyzed = analyzer.calculate_indicators(df)
    df_analyzed = analyzer.detect_stage_transitions(df_analyzed)

    # Get latest data
    latest = df_analyzed.iloc[-1]
    recent_data = df_analyzed.tail(30)

    # Display results
    print(f"Current Price: ₹{latest['CLOSE_PRICE']:.2f}")
    print(f"MA200: ₹{latest['MA200']:.2f}")
    print(f"Price vs MA200: {latest['price_vs_ma']:+.2f}%")
    print(f"MA200 Slope: {latest['MA200_slope']:+.4f}")
    print(f"Volatility: {latest['volatility_pct']:.2f}%")
    print(f"Volume Ratio: {latest['volume_ratio']:.2f}x")
    print(f"5-day Price Change: {latest['price_change_5d']:+.2f}%")
    print(f"10-day Price Change: {latest['price_change_10d']:+.2f}%")
    print(f"Delivery %: {latest['DELIV_PER']:.1f}%")
    print(f"No. of Trades: {latest['NO_OF_TRADES']:,}")
    print()

    # Current stage
    if latest['stage1']:
        print("🔄 Current Stage: STAGE 1 (Accumulation)")
        print("   - Price consolidating around MA200")
        print("   - Low volatility environment")
        print("   - Potential for future breakout")
    elif latest['stage2']:
        print("📈 Current Stage: STAGE 2 (Advancing)")
        print("   - Price above MA200 with momentum")
        print("   - Rising volume confirmation")
        print("   - Uptrend in progress")
    else:
        print("⚠️  Current Stage: TRANSITION or STAGE 3/4")
        print("   - Not in clear accumulation or advancing phase")

    print()

    # Recent stage 2 entries
    stage2_entries = recent_data[recent_data['stage2_entry']]
    if not stage2_entries.empty:
        print("📈 Recent Stage 2 Entries:")
        for date, row in stage2_entries.iterrows():
            print(f"   {date.strftime('%Y-%m-%d')}: ₹{row['CLOSE_PRICE']:.2f}")

    return {
        'symbol': symbol,
        'current_price': latest['CLOSE_PRICE'],
        'ma200': latest['MA200'],
        'price_vs_ma': latest['price_vs_ma'],
        'volatility': latest['volatility_pct'],
        'volume_ratio': latest['volume_ratio'],
        'delivery_pct': latest['DELIV_PER'],
        'stage1': latest['stage1'],
        'stage2': latest['stage2'],
        'dataframe': df_analyzed
    }

def main():
    """Main function demonstrating the complete stage analysis system"""

    print("🚀 COMPLETE STAGE ANALYSIS SYSTEM")
    print("=" * 50)

    # MySQL configuration - modify these values for your setup
    mysql_config = {
        'user': 'root',
        'password': 'rootroot',
        'host': 'localhost',
        'database': 'delivery',
        'raise_on_warnings': True
    }

    # Test symbols
    symbols = ['RELIANCE', 'TCS', 'INFY', 'HDFC', 'ICICI']

    if MYSQL_AVAILABLE:
        print("📊 Using MySQL database for analysis...")

        try:
            # Analyze stocks from MySQL
            recent_stage2, current_stage1, stock_data = analyze_stocks_from_mysql(
                symbols=symbols,
                mysql_config=mysql_config,
                recent_date=None,  # Use current date
                lookback_days=5,
                limit=252  # ~1 year of data
            )

            # Display results
            display_analysis_results(recent_stage2, current_stage1, stock_data)

            # Single stock detailed analysis
            if symbols:
                print("\n" + "="*70)
                analyze_single_stock(symbols[0], mysql_config)

        except Exception as e:
            print(f"❌ MySQL analysis failed: {e}")
            print("🔄 Falling back to dummy data...")

            # Fallback to dummy data
            dummy_data = generate_dummy_data(symbols)
            analyzer = StageAnalyzer()
            recent_stage2, current_stage1 = analyzer.analyze_stocks(dummy_data, lookback_days=5)
            display_analysis_results(recent_stage2, current_stage1, dummy_data)

    else:
        print("📊 MySQL not available, using dummy data for demonstration...")

        # Use dummy data
        dummy_data = generate_dummy_data(symbols)
        analyzer = StageAnalyzer()
        recent_stage2, current_stage1 = analyzer.analyze_stocks(dummy_data, lookback_days=5)
        display_analysis_results(recent_stage2, current_stage1, dummy_data)

        # Single stock analysis with dummy data
        if symbols:
            print("\n" + "="*70)
            analyze_single_stock(symbols[0])

    # Summary
    print("\n" + "="*70)
    print("📊 ANALYSIS COMPLETE")
    print("-" * 30)
    print("✅ Stage Analysis system ready")
    print("✅ MySQL integration available" if MYSQL_AVAILABLE else "⚠️  Install mysql-connector-python for MySQL support")
    print("✅ Dummy data fallback working")
    print("\n💡 To customize:")
    print("   - Modify mysql_config for your database")
    print("   - Update symbols list")
    print("   - Adjust analysis parameters")
    print("   - Set up automated screening")

if __name__ == "__main__":
    main()
