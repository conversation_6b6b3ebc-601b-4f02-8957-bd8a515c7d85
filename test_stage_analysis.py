"""
Test script for Stage Analysis functionality
"""

import pandas as pd
import numpy as np
from stage_analysis import StageAnalyzer, generate_dummy_data
from datetime import datetime, timed<PERSON><PERSON>

def test_stage_analyzer():
    """Test the StageAnalyzer class with known data patterns"""
    
    print("🧪 TESTING STAGE ANALYSIS FUNCTIONALITY")
    print("=" * 50)
    
    # Create test data with known patterns
    dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
    dates = dates[dates.weekday < 5]  # Remove weekends
    
    # Test Case 1: Clear Stage 1 pattern (sideways movement around MA)
    print("Test 1: Stage 1 Pattern (Accumulation)")
    print("-" * 40)
    
    n_days = len(dates)
    base_price = 100
    
    # Create sideways movement around base price
    stage1_prices = base_price + np.random.normal(0, 2, n_days)  # Low volatility sideways
    stage1_volumes = np.random.uniform(800000, 1200000, n_days)  # Stable volume
    
    df_stage1 = pd.DataFrame({
        'DATE1': dates,
        'OPEN_PRICE': stage1_prices * 0.999,
        'HIGH_PRICE': stage1_prices * 1.01,
        'LOW_PRICE': stage1_prices * 0.99,
        'CLOSE_PRICE': stage1_prices,
        'PREV_CLOSE': np.roll(stage1_prices, 1),
        'TTL_TRD_QNTY': stage1_volumes,
        'DELIV_QTY': stage1_volumes * 0.5,
        'DELIV_PER': np.full(n_days, 50),
        'NO_OF_TRADES': (stage1_volumes / 1000).astype(int)
    })
    df_stage1.set_index('DATE1', inplace=True)
    
    analyzer = StageAnalyzer()
    df_analyzed = analyzer.calculate_indicators(df_stage1)
    df_analyzed = analyzer.detect_stage_transitions(df_analyzed)
    
    # Check recent data for Stage 1 conditions
    recent_stage1 = df_analyzed.tail(50)['stage1'].sum()
    recent_stage2 = df_analyzed.tail(50)['stage2'].sum()
    
    print(f"Stage 1 signals in last 50 days: {recent_stage1}")
    print(f"Stage 2 signals in last 50 days: {recent_stage2}")
    print(f"Expected: High Stage 1, Low Stage 2")
    print()
    
    # Test Case 2: Clear Stage 2 pattern (breakout with volume)
    print("Test 2: Stage 2 Pattern (Breakout)")
    print("-" * 40)
    
    # Create breakout pattern
    stage2_prices = np.concatenate([
        np.full(200, 100),  # Base accumulation
        100 + np.cumsum(np.random.uniform(0.1, 0.5, n_days - 200))  # Uptrend
    ])
    
    # Higher volume during breakout
    stage2_volumes = np.concatenate([
        np.random.uniform(800000, 1200000, 200),  # Normal volume
        np.random.uniform(1500000, 2500000, n_days - 200)  # Higher volume
    ])
    
    df_stage2 = pd.DataFrame({
        'DATE1': dates,
        'OPEN_PRICE': stage2_prices * 0.999,
        'HIGH_PRICE': stage2_prices * 1.02,
        'LOW_PRICE': stage2_prices * 0.98,
        'CLOSE_PRICE': stage2_prices,
        'PREV_CLOSE': np.roll(stage2_prices, 1),
        'TTL_TRD_QNTY': stage2_volumes,
        'DELIV_QTY': stage2_volumes * 0.6,
        'DELIV_PER': np.full(n_days, 60),
        'NO_OF_TRADES': (stage2_volumes / 1000).astype(int)
    })
    df_stage2.set_index('DATE1', inplace=True)
    
    df_analyzed2 = analyzer.calculate_indicators(df_stage2)
    df_analyzed2 = analyzer.detect_stage_transitions(df_analyzed2)
    
    recent_stage1_2 = df_analyzed2.tail(50)['stage1'].sum()
    recent_stage2_2 = df_analyzed2.tail(50)['stage2'].sum()
    stage2_entries = df_analyzed2['stage2_entry'].sum()
    
    print(f"Stage 1 signals in last 50 days: {recent_stage1_2}")
    print(f"Stage 2 signals in last 50 days: {recent_stage2_2}")
    print(f"Total Stage 2 entries detected: {stage2_entries}")
    print(f"Expected: Low Stage 1, High Stage 2, Multiple entries")
    print()
    
    # Test Case 3: Portfolio analysis with mixed patterns
    print("Test 3: Portfolio Analysis")
    print("-" * 40)
    
    test_portfolio = {
        'STAGE1_STOCK': df_stage1,
        'STAGE2_STOCK': df_stage2
    }
    
    recent_entries, current_stage1 = analyzer.analyze_stocks(test_portfolio, lookback_days=10)
    
    print(f"Recent Stage 2 entries found: {len(recent_entries)}")
    print(f"Current Stage 1 stocks found: {len(current_stage1)}")
    
    for entry in recent_entries:
        print(f"  - {entry['symbol']}: Entry on {entry['entry_date'].strftime('%Y-%m-%d')}")
    
    for stock in current_stage1:
        print(f"  - {stock['symbol']}: Price vs MA = {stock['price_vs_ma']:.2f}%")
    
    print()
    
    # Test Case 4: Edge cases and data validation
    print("Test 4: Data Validation")
    print("-" * 40)
    
    # Test with insufficient data
    short_df = df_stage1.tail(50)  # Less than MA period
    try:
        short_portfolio = {'SHORT_DATA': short_df}
        recent, stage1 = analyzer.analyze_stocks(short_portfolio)
        print(f"Short data handling: OK (found {len(recent)} + {len(stage1)} results)")
    except Exception as e:
        print(f"Short data handling: Error - {e}")
    
    # Test with missing values
    df_with_na = df_stage1.copy()
    df_with_na.loc[df_with_na.index[-10:], 'CLOSE_PRICE'] = np.nan
    try:
        na_portfolio = {'NA_DATA': df_with_na}
        recent, stage1 = analyzer.analyze_stocks(na_portfolio)
        print(f"Missing data handling: OK (found {len(recent)} + {len(stage1)} results)")
    except Exception as e:
        print(f"Missing data handling: Error - {e}")
    
    print("\n✅ All tests completed!")

def test_dummy_data_generation():
    """Test the dummy data generation function"""
    
    print("\n🎲 TESTING DUMMY DATA GENERATION")
    print("=" * 50)
    
    symbols = ['TEST_A', 'TEST_B', 'TEST_C']
    data = generate_dummy_data(symbols, start_date='2023-01-01', end_date='2024-12-31')
    
    print(f"Generated data for {len(data)} symbols")
    
    for symbol, df in data.items():
        print(f"\n{symbol}:")
        print(f"  - Data points: {len(df)}")
        print(f"  - Date range: {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}")
        print(f"  - Price range: ₹{df['CLOSE_PRICE'].min():.2f} - ₹{df['CLOSE_PRICE'].max():.2f}")
        print(f"  - Average volume: {df['TTL_TRD_QNTY'].mean():,.0f}")
        
        # Check data integrity
        missing_data = df.isnull().sum().sum()
        print(f"  - Missing values: {missing_data}")
        
        # Check OHLC relationships
        ohlc_valid = (
            (df['HIGH_PRICE'] >= df['OPEN_PRICE']).all() and
            (df['HIGH_PRICE'] >= df['CLOSE_PRICE']).all() and
            (df['LOW_PRICE'] <= df['OPEN_PRICE']).all() and
            (df['LOW_PRICE'] <= df['CLOSE_PRICE']).all()
        )
        print(f"  - OHLC relationships valid: {ohlc_valid}")

def main():
    """Run all tests"""
    test_stage_analyzer()
    test_dummy_data_generation()
    
    print("\n🎯 TESTING SUMMARY")
    print("=" * 50)
    print("✅ Stage Analysis functionality tested")
    print("✅ Dummy data generation tested")
    print("✅ Portfolio analysis tested")
    print("✅ Edge cases handled")
    print("\nThe Stage Analysis system is ready for use!")

if __name__ == "__main__":
    main()
