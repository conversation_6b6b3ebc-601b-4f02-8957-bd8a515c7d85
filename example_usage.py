"""
Example usage of the Stage Analysis script with real data format
This script shows how to use the StageAnalyzer with your actual DataFrame structure
"""

import pandas as pd
import numpy as np
from stage_analysis import StageAnaly<PERSON>, generate_dummy_data
from datetime import datetime, timed<PERSON>ta

def load_your_data():
    """
    Replace this function with your actual data loading logic
    This is just an example of how your data should be structured
    """
    # Example: Load from CSV files or database
    # Your actual implementation might look like:
    # 
    # stock_data = {}
    # for symbol in your_stock_list:
    #     df = pd.read_csv(f'data/{symbol}.csv')
    #     df['DATE1'] = pd.to_datetime(df['DATE1'])
    #     df.set_index('DATE1', inplace=True)
    #     stock_data[symbol] = df
    # 
    # return stock_data
    
    # For demonstration, we'll use the dummy data generator
    symbols = ['RELIANCE', 'TCS', 'INFY', 'HDFC', 'ICICI', 'WIPRO', 'BHARTI', 'ITC']
    return generate_dummy_data(symbols, start_date='2023-01-01', end_date='2024-12-31')

def analyze_portfolio(stock_data, lookback_days=5):
    """Analyze a portfolio of stocks for stage analysis"""
    
    analyzer = StageAnalyzer(ma_period=200, volatility_window=20, volume_window=20)
    
    print("🔍 PORTFOLIO STAGE ANALYSIS")
    print("=" * 50)
    print(f"Analyzing {len(stock_data)} stocks...")
    print(f"Looking for Stage 2 entries in last {lookback_days} days")
    print()
    
    # Analyze all stocks
    recent_stage2, current_stage1 = analyzer.analyze_stocks(stock_data, lookback_days)
    
    # Display detailed results
    print(f"📈 RECENT STAGE 2 ENTRIES: {len(recent_stage2)} stocks")
    print("-" * 60)
    
    if recent_stage2:
        # Sort by entry date (most recent first)
        recent_stage2.sort(key=lambda x: x['entry_date'], reverse=True)
        
        for i, entry in enumerate(recent_stage2, 1):
            gain_loss = ((entry['current_price'] - entry['entry_price']) / entry['entry_price']) * 100
            print(f"{i}. {entry['symbol']}")
            print(f"   📅 Entry Date: {entry['entry_date'].strftime('%Y-%m-%d')}")
            print(f"   💰 Entry Price: ₹{entry['entry_price']:.2f}")
            print(f"   📊 Current Price: ₹{entry['current_price']:.2f}")
            print(f"   📈 Gain/Loss: {gain_loss:+.2f}%")
            print(f"   📉 MA200: ₹{entry['ma200']:.2f}")
            print(f"   📊 Volume Ratio: {entry['volume_ratio']:.2f}x")
            print(f"   🎯 Price above MA: {((entry['current_price'] - entry['ma200']) / entry['ma200'] * 100):+.2f}%")
            print()
    else:
        print("   No recent Stage 2 entries found.")
        print()
    
    print(f"🔄 CURRENT STAGE 1 STOCKS (Accumulation Phase): {len(current_stage1)} stocks")
    print("-" * 60)
    
    if current_stage1:
        # Sort by how close to MA200 (best accumulation candidates first)
        current_stage1.sort(key=lambda x: abs(x['price_vs_ma']))
        
        for i, stock in enumerate(current_stage1, 1):
            print(f"{i}. {stock['symbol']}")
            print(f"   💰 Current Price: ₹{stock['current_price']:.2f}")
            print(f"   📉 MA200: ₹{stock['ma200']:.2f}")
            print(f"   📊 Price vs MA: {stock['price_vs_ma']:+.2f}%")
            print(f"   📈 Volatility: {stock['volatility']:.2f}%")
            print(f"   📊 Volume Ratio: {stock['volume_ratio']:.2f}x")
            
            # Accumulation quality score
            quality_score = (
                (5 - abs(stock['price_vs_ma'])) * 0.4 +  # Closer to MA is better
                (3 - stock['volatility']) * 0.3 +         # Lower volatility is better
                (2 - abs(stock['volume_ratio'] - 1)) * 0.3  # Volume near average is better
            )
            quality_score = max(0, min(10, quality_score))
            
            print(f"   ⭐ Accumulation Quality: {quality_score:.1f}/10")
            print()
    else:
        print("   No stocks currently in Stage 1 accumulation phase.")
        print()
    
    return recent_stage2, current_stage1

def get_detailed_analysis(stock_data, symbol):
    """Get detailed analysis for a specific stock"""
    if symbol not in stock_data:
        print(f"Stock {symbol} not found in data.")
        return
    
    analyzer = StageAnalyzer()
    df = stock_data[symbol].copy()
    
    # Calculate all indicators
    df_analyzed = analyzer.calculate_indicators(df)
    df_analyzed = analyzer.detect_stage_transitions(df_analyzed)
    
    # Get recent data (last 30 days)
    recent_data = df_analyzed.tail(30)
    latest = df_analyzed.iloc[-1]
    
    print(f"📊 DETAILED ANALYSIS: {symbol}")
    print("=" * 50)
    print(f"Current Price: ₹{latest['CLOSE_PRICE']:.2f}")
    print(f"MA200: ₹{latest['MA200']:.2f}")
    print(f"Price vs MA200: {latest['price_vs_ma']:+.2f}%")
    print(f"MA200 Slope: {latest['MA200_slope']:+.4f}")
    print(f"Volatility: {latest['volatility_pct']:.2f}%")
    print(f"Volume Ratio: {latest['volume_ratio']:.2f}x")
    print(f"5-day Price Change: {latest['price_change_5d']:+.2f}%")
    print(f"10-day Price Change: {latest['price_change_10d']:+.2f}%")
    print()
    
    # Current stage
    if latest['stage1']:
        print("🔄 Current Stage: STAGE 1 (Accumulation)")
        print("   - Price consolidating around MA200")
        print("   - Low volatility environment")
        print("   - Potential for future breakout")
    elif latest['stage2']:
        print("📈 Current Stage: STAGE 2 (Advancing)")
        print("   - Price above MA200 with momentum")
        print("   - Rising volume confirmation")
        print("   - Uptrend in progress")
    else:
        print("⚠️  Current Stage: TRANSITION or STAGE 3/4")
        print("   - Not in clear accumulation or advancing phase")
    
    print()
    
    # Recent stage 2 entries
    stage2_entries = recent_data[recent_data['stage2_entry']]
    if not stage2_entries.empty:
        print("📈 Recent Stage 2 Entries:")
        for date, row in stage2_entries.iterrows():
            print(f"   {date.strftime('%Y-%m-%d')}: ₹{row['CLOSE_PRICE']:.2f}")
    
    return df_analyzed

def main():
    """Main function demonstrating the usage"""
    
    # Load your stock data
    print("Loading stock data...")
    stock_data = load_your_data()
    
    # Analyze portfolio
    recent_stage2, current_stage1 = analyze_portfolio(stock_data, lookback_days=5)
    
    # Example: Get detailed analysis for a specific stock
    if stock_data:
        sample_symbol = list(stock_data.keys())[0]
        print("\n" + "="*70)
        detailed_analysis = get_detailed_analysis(stock_data, sample_symbol)
    
    # Summary statistics
    print("\n" + "="*70)
    print("📊 PORTFOLIO SUMMARY")
    print("-" * 30)
    print(f"Total Stocks Analyzed: {len(stock_data)}")
    print(f"Recent Stage 2 Entries: {len(recent_stage2)}")
    print(f"Current Stage 1 Stocks: {len(current_stage1)}")
    
    if recent_stage2:
        avg_gain = np.mean([((s['current_price'] - s['entry_price']) / s['entry_price']) * 100 
                           for s in recent_stage2])
        print(f"Average Gain on Stage 2 Entries: {avg_gain:+.2f}%")

if __name__ == "__main__":
    main()
