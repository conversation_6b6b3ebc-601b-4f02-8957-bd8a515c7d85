"""
Visualization script for Stage Analysis
Requires matplotlib for plotting
"""

try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("Matplotlib not available. Install with: pip install matplotlib")

import pandas as pd
import numpy as np
from stage_analysis import StageAnalyzer, generate_dummy_data

def plot_stage_analysis(symbol, df, save_plot=True):
    """Plot stage analysis for a single stock"""
    
    if not MATPLOTLIB_AVAILABLE:
        print("Matplotlib required for plotting. Install with: pip install matplotlib")
        return
    
    analyzer = StageAnalyzer()
    df_analyzed = analyzer.calculate_indicators(df)
    df_analyzed = analyzer.detect_stage_transitions(df_analyzed)
    
    # Create the plot
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
    fig.suptitle(f'Stage Analysis: {symbol}', fontsize=16, fontweight='bold')
    
    # Plot 1: Price and MA200 with stage highlights
    ax1.plot(df_analyzed.index, df_analyzed['CLOSE_PRICE'], label='Close Price', linewidth=1.5, color='black')
    ax1.plot(df_analyzed.index, df_analyzed['MA200'], label='MA200', linewidth=2, color='blue', alpha=0.7)
    
    # Highlight Stage 1 periods
    stage1_periods = df_analyzed[df_analyzed['stage1']]
    if not stage1_periods.empty:
        ax1.scatter(stage1_periods.index, stage1_periods['CLOSE_PRICE'], 
                   color='orange', alpha=0.6, s=20, label='Stage 1 (Accumulation)', zorder=5)
    
    # Highlight Stage 2 periods
    stage2_periods = df_analyzed[df_analyzed['stage2']]
    if not stage2_periods.empty:
        ax1.scatter(stage2_periods.index, stage2_periods['CLOSE_PRICE'], 
                   color='green', alpha=0.8, s=30, label='Stage 2 (Advancing)', zorder=5)
    
    # Mark Stage 2 entries
    stage2_entries = df_analyzed[df_analyzed['stage2_entry']]
    if not stage2_entries.empty:
        ax1.scatter(stage2_entries.index, stage2_entries['CLOSE_PRICE'], 
                   color='red', s=100, marker='^', label='Stage 2 Entry', zorder=10)
    
    ax1.set_ylabel('Price (₹)')
    ax1.set_title('Price Action with Stage Analysis')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Volume with ratio
    ax2.bar(df_analyzed.index, df_analyzed['TTL_TRD_QNTY'], alpha=0.6, color='lightblue', width=1)
    ax2.plot(df_analyzed.index, df_analyzed['volume_ma'], color='red', linewidth=2, label='Volume MA')
    
    # Highlight high volume periods
    high_volume = df_analyzed[df_analyzed['volume_ratio'] > 1.5]
    if not high_volume.empty:
        ax2.bar(high_volume.index, high_volume['TTL_TRD_QNTY'], 
               alpha=0.8, color='red', width=1, label='High Volume')
    
    ax2.set_ylabel('Volume')
    ax2.set_title('Volume Analysis')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Technical indicators
    ax3_twin = ax3.twinx()
    
    # Price vs MA200 percentage
    ax3.plot(df_analyzed.index, df_analyzed['price_vs_ma'], 
             color='purple', linewidth=1.5, label='Price vs MA200 (%)')
    ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax3.axhline(y=5, color='green', linestyle='--', alpha=0.5, label='Stage 1 Upper Bound')
    ax3.axhline(y=-5, color='green', linestyle='--', alpha=0.5, label='Stage 1 Lower Bound')
    
    # Volatility
    ax3_twin.plot(df_analyzed.index, df_analyzed['volatility_pct'], 
                  color='orange', linewidth=1, alpha=0.7, label='Volatility %')
    
    ax3.set_ylabel('Price vs MA200 (%)', color='purple')
    ax3_twin.set_ylabel('Volatility (%)', color='orange')
    ax3.set_xlabel('Date')
    ax3.set_title('Technical Indicators')
    ax3.grid(True, alpha=0.3)
    
    # Format x-axis
    for ax in [ax1, ax2, ax3]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    if save_plot:
        filename = f'stage_analysis_{symbol}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"Plot saved as {filename}")
    
    plt.show()
    
    return fig

def plot_portfolio_summary(data_dict, save_plot=True):
    """Create a summary plot for the entire portfolio"""
    
    if not MATPLOTLIB_AVAILABLE:
        print("Matplotlib required for plotting. Install with: pip install matplotlib")
        return
    
    analyzer = StageAnalyzer()
    recent_stage2, current_stage1 = analyzer.analyze_stocks(data_dict, lookback_days=5)
    
    # Create summary statistics
    total_stocks = len(data_dict)
    stage2_count = len(recent_stage2)
    stage1_count = len(current_stage1)
    other_count = total_stocks - stage2_count - stage1_count
    
    # Create pie chart
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Portfolio distribution
    labels = ['Recent Stage 2', 'Current Stage 1', 'Other Stages']
    sizes = [stage2_count, stage1_count, other_count]
    colors = ['green', 'orange', 'lightgray']
    
    ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax1.set_title('Portfolio Stage Distribution')
    
    # Performance of Stage 2 entries
    if recent_stage2:
        symbols = [entry['symbol'] for entry in recent_stage2]
        gains = [((entry['current_price'] - entry['entry_price']) / entry['entry_price']) * 100 
                for entry in recent_stage2]
        
        colors_bar = ['green' if g >= 0 else 'red' for g in gains]
        bars = ax2.bar(range(len(symbols)), gains, color=colors_bar, alpha=0.7)
        
        ax2.set_xlabel('Stocks')
        ax2.set_ylabel('Gain/Loss (%)')
        ax2.set_title('Stage 2 Entry Performance')
        ax2.set_xticks(range(len(symbols)))
        ax2.set_xticklabels(symbols, rotation=45)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax2.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, gain in zip(bars, gains):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + (0.5 if height >= 0 else -1),
                    f'{gain:.1f}%', ha='center', va='bottom' if height >= 0 else 'top')
    else:
        ax2.text(0.5, 0.5, 'No Recent Stage 2 Entries', ha='center', va='center', 
                transform=ax2.transAxes, fontsize=14)
        ax2.set_title('Stage 2 Entry Performance')
    
    plt.tight_layout()
    
    if save_plot:
        filename = 'portfolio_stage_summary.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"Portfolio summary saved as {filename}")
    
    plt.show()
    
    return fig

def main():
    """Main function to demonstrate visualization"""
    
    if not MATPLOTLIB_AVAILABLE:
        print("Please install matplotlib to use visualization features:")
        print("pip install matplotlib")
        return
    
    print("📊 STAGE ANALYSIS VISUALIZATION")
    print("=" * 50)
    
    # Generate sample data
    symbols = ['RELIANCE', 'TCS', 'INFY']
    print("Generating sample data...")
    data_dict = generate_dummy_data(symbols, start_date='2023-06-01', end_date='2024-12-31')
    
    # Plot individual stock analysis
    print(f"\nCreating individual plots for {len(symbols)} stocks...")
    for symbol in symbols:
        print(f"Plotting {symbol}...")
        plot_stage_analysis(symbol, data_dict[symbol], save_plot=True)
    
    # Plot portfolio summary
    print("\nCreating portfolio summary...")
    plot_portfolio_summary(data_dict, save_plot=True)
    
    print("\n✅ All visualizations created!")
    print("Check the generated PNG files for detailed charts.")

if __name__ == "__main__":
    main()
