#!/bin/bash

# Stage Analysis Virtual Environment Activation Script
# This script activates the virtual environment and runs the stage analysis

echo "🚀 STAGE ANALYSIS SYSTEM"
echo "========================"

# Check if virtual environment exists
if [ ! -d "stage_analysis_env" ]; then
    echo "❌ Virtual environment not found!"
    echo "Please run the setup first:"
    echo "   python3 -m venv stage_analysis_env"
    echo "   source stage_analysis_env/bin/activate"
    echo "   pip install -r requirements.txt"
    exit 1
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source stage_analysis_env/bin/activate

# Check if dependencies are installed
echo "📦 Checking dependencies..."
python3 -c "import pandas, numpy, mysql.connector" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ Dependencies not installed!"
    echo "Installing dependencies..."
    pip install -r requirements.txt
fi

echo "✅ Environment ready!"
echo ""

# Run the analysis based on argument
case "$1" in
    "complete")
        echo "🔍 Running complete stage analysis..."
        python3 complete_stage_analysis.py
        ;;
    "examples")
        echo "📚 Running usage examples..."
        python3 usage_examples.py
        ;;
    "test")
        echo "🧪 Running MySQL connection test..."
        python3 test_mysql_connection.py
        ;;
    "config")
        echo "⚙️  Running configuration test..."
        python3 config.py
        ;;
    *)
        echo "📋 Available commands:"
        echo "   ./run_stage_analysis.sh complete  - Run complete stage analysis"
        echo "   ./run_stage_analysis.sh examples  - Run usage examples"
        echo "   ./run_stage_analysis.sh test      - Test MySQL connection"
        echo "   ./run_stage_analysis.sh config    - Test configuration"
        echo ""
        echo "🔧 Manual activation:"
        echo "   source stage_analysis_env/bin/activate"
        echo "   python3 complete_stage_analysis.py"
        ;;
esac

echo ""
echo "✅ Done!"
